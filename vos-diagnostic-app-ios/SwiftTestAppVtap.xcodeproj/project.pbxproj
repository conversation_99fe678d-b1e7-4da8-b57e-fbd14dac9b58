// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		342493032539A33300D45B40 /* activity_indicator.json in Resources */ = {isa = PBXBuildFile; fileRef = 342493022539A33300D45B40 /* activity_indicator.json */; };
		34376F6122B0BD28005130FF /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 34376F5F22B0BD28005130FF /* Main.storyboard */; };
		34376F6322B0BD2A005130FF /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 34376F6222B0BD2A005130FF /* Assets.xcassets */; };
		34376F6622B0BD2A005130FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 34376F6422B0BD2A005130FF /* LaunchScreen.storyboard */; };
		34376F9122B0D42D005130FF /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 34376F9022B0D42D005130FF /* libc++.tbd */; };
		34376F9322B0D434005130FF /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 34376F9222B0D434005130FF /* libz.tbd */; };
		34376F9622B0DCA1005130FF /* DeviceManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34376F9522B0DCA1005130FF /* DeviceManager.swift */; };
		34376F9822B0DCB5005130FF /* LocalAuthentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 34376F9722B0DCB5005130FF /* LocalAuthentication.framework */; };
		34376F9A22B0DCD5005130FF /* UserDefaultsHandle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34376F9922B0DCD5005130FF /* UserDefaultsHandle.swift */; };
		34376FA222B0DD25005130FF /* UIView+Toast.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34376F9C22B0DD25005130FF /* UIView+Toast.swift */; };
		34376FA322B0DD25005130FF /* UIColor+Hex.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34376F9D22B0DD25005130FF /* UIColor+Hex.swift */; };
		34376FA522B0DD25005130FF /* Observable+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34376F9F22B0DD25005130FF /* Observable+Extensions.swift */; };
		34376FA622B0DD25005130FF /* UIViewController+ScrollForKeyboard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34376FA022B0DD25005130FF /* UIViewController+ScrollForKeyboard.swift */; };
		34376FA722B0DD25005130FF /* UIDevice+Platform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34376FA122B0DD25005130FF /* UIDevice+Platform.swift */; };
		34376FA922B0DF4C005130FF /* Resource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34376FA822B0DF4C005130FF /* Resource.swift */; };
		343E1B9123040C580044669B /* ProgressBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 343E1B9023040C580044669B /* ProgressBar.swift */; };
		343E1B9323040CBB0044669B /* UIColor+Theme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 343E1B9223040CBA0044669B /* UIColor+Theme.swift */; };
		344E692D22B8F79900C24847 /* Data+HexString.swift in Sources */ = {isa = PBXBuildFile; fileRef = 344E692C22B8F79800C24847 /* Data+HexString.swift */; };
		3490829D2305068E00527289 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 3490829F2305068E00527289 /* Localizable.strings */; };
		84A54BFA26A6E7C50072A6D0 /* firmware in Resources */ = {isa = PBXBuildFile; fileRef = 84A54BF526A6E7C50072A6D0 /* firmware */; };
		84A54BFB26A6E7C50072A6D0 /* signature in Resources */ = {isa = PBXBuildFile; fileRef = 84A54BF626A6E7C50072A6D0 /* signature */; };
		84A54BFD26A6E7C50072A6D0 /* profile in Resources */ = {isa = PBXBuildFile; fileRef = 84A54BF826A6E7C50072A6D0 /* profile */; };
		84A54BFE26A6E7C50072A6D0 /* vkeylicensepack in Resources */ = {isa = PBXBuildFile; fileRef = 84A54BF926A6E7C50072A6D0 /* vkeylicensepack */; };
		8813F7534DCB5C30621DABC3 /* Pods_V_Key_Insights.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F57704F5847910B6B4883821 /* Pods_V_Key_Insights.framework */; };
		8C65BF8F2D898B270082BF30 /* VGuard.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C65BF8D2D898B270082BF30 /* VGuard.xcframework */; };
		8C65BF902D898B270082BF30 /* VosWrapper.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C65BF8E2D898B270082BF30 /* VosWrapper.xcframework */; };
		8C8BEF452BABE1080098CFF7 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 8C8BEF442BABE1080098CFF7 /* PrivacyInfo.xcprivacy */; };
		8CBD2CBB2E14D3CF005917DF /* UserGuideViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8CBD2CB92E14D3CF005917DF /* UserGuideViewController.swift */; };
		8CBD2CBD2E14D3CF005917DF /* ResultDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8CBD2CB82E14D3CF005917DF /* ResultDetailViewController.swift */; };
		8CBD2CBF2E14D3CF005917DF /* ActivityIndicatorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8CBD2CB12E14D3CF005917DF /* ActivityIndicatorView.swift */; };
		8CBD2CC32E14D3CF005917DF /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8CBD2CBA2E14D3CF005917DF /* ViewController.swift */; };
		8CBD2CC42E14D3CF005917DF /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8CBD2CB22E14D3CF005917DF /* AppDelegate.swift */; };
		8CFD8A952B83599200DDD9A6 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8CFD8A942B83599200DDD9A6 /* CoreLocation.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2CCCA2ABC67969E8E37B8673 /* Pods-V-Key Insights.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-V-Key Insights.release.xcconfig"; path = "Target Support Files/Pods-V-Key Insights/Pods-V-Key Insights.release.xcconfig"; sourceTree = "<group>"; };
		342493022539A33300D45B40 /* activity_indicator.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; name = activity_indicator.json; path = SwiftTestAppVtap/activity_indicator.json; sourceTree = "<group>"; };
		34376F5822B0BD28005130FF /* V-Key Insights.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "V-Key Insights.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		34376F6222B0BD2A005130FF /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		34376F6722B0BD2A005130FF /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		34376F8A22B0D24F005130FF /* SwiftTestAppVtap.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SwiftTestAppVtap.entitlements; sourceTree = "<group>"; };
		34376F8B22B0D291005130FF /* SwiftTestAppVtap-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SwiftTestAppVtap-Bridging-Header.h"; sourceTree = "<group>"; };
		34376F9022B0D42D005130FF /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		34376F9222B0D434005130FF /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		34376F9522B0DCA1005130FF /* DeviceManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DeviceManager.swift; sourceTree = "<group>"; };
		34376F9722B0DCB5005130FF /* LocalAuthentication.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = LocalAuthentication.framework; path = System/Library/Frameworks/LocalAuthentication.framework; sourceTree = SDKROOT; };
		34376F9922B0DCD5005130FF /* UserDefaultsHandle.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserDefaultsHandle.swift; sourceTree = "<group>"; };
		34376F9C22B0DD25005130FF /* UIView+Toast.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIView+Toast.swift"; sourceTree = "<group>"; };
		34376F9D22B0DD25005130FF /* UIColor+Hex.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIColor+Hex.swift"; sourceTree = "<group>"; };
		34376F9F22B0DD25005130FF /* Observable+Extensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Observable+Extensions.swift"; sourceTree = "<group>"; };
		34376FA022B0DD25005130FF /* UIViewController+ScrollForKeyboard.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIViewController+ScrollForKeyboard.swift"; sourceTree = "<group>"; };
		34376FA122B0DD25005130FF /* UIDevice+Platform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIDevice+Platform.swift"; sourceTree = "<group>"; };
		34376FA822B0DF4C005130FF /* Resource.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Resource.swift; sourceTree = "<group>"; };
		343E1B9023040C580044669B /* ProgressBar.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProgressBar.swift; sourceTree = "<group>"; };
		343E1B9223040CBA0044669B /* UIColor+Theme.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIColor+Theme.swift"; sourceTree = "<group>"; };
		344E692C22B8F79800C24847 /* Data+HexString.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "Data+HexString.swift"; sourceTree = "<group>"; };
		349082992305054F00527289 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		3490829A2305054F00527289 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		3490829E2305068E00527289 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		84A54BF526A6E7C50072A6D0 /* firmware */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = firmware; sourceTree = "<group>"; };
		84A54BF626A6E7C50072A6D0 /* signature */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = signature; sourceTree = "<group>"; };
		84A54BF826A6E7C50072A6D0 /* profile */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = profile; sourceTree = "<group>"; };
		84A54BF926A6E7C50072A6D0 /* vkeylicensepack */ = {isa = PBXFileReference; lastKnownFileType = file; path = vkeylicensepack; sourceTree = "<group>"; };
		8C65BF8D2D898B270082BF30 /* VGuard.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:63DA3SWC73:V-Key Pte Ltd"; lastKnownFileType = wrapper.xcframework; path = VGuard.xcframework; sourceTree = "<group>"; };
		8C65BF8E2D898B270082BF30 /* VosWrapper.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = VosWrapper.xcframework; sourceTree = "<group>"; };
		8C7B6F042B8F1A13006E3C7B /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Main.strings; sourceTree = "<group>"; };
		8C8BEF442BABE1080098CFF7 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		8CBD2CB12E14D3CF005917DF /* ActivityIndicatorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActivityIndicatorView.swift; sourceTree = "<group>"; };
		8CBD2CB22E14D3CF005917DF /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		8CBD2CB82E14D3CF005917DF /* ResultDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultDetailViewController.swift; sourceTree = "<group>"; };
		8CBD2CB92E14D3CF005917DF /* UserGuideViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserGuideViewController.swift; sourceTree = "<group>"; };
		8CBD2CBA2E14D3CF005917DF /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		8CFD8A942B83599200DDD9A6 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		9018CC4CD5A6456E002860E7 /* Pods-V-Key Insights.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-V-Key Insights.debug.xcconfig"; path = "Target Support Files/Pods-V-Key Insights/Pods-V-Key Insights.debug.xcconfig"; sourceTree = "<group>"; };
		F57704F5847910B6B4883821 /* Pods_V_Key_Insights.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_V_Key_Insights.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		34376F5522B0BD28005130FF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8C65BF8F2D898B270082BF30 /* VGuard.xcframework in Frameworks */,
				8C65BF902D898B270082BF30 /* VosWrapper.xcframework in Frameworks */,
				34376F9822B0DCB5005130FF /* LocalAuthentication.framework in Frameworks */,
				8CFD8A952B83599200DDD9A6 /* CoreLocation.framework in Frameworks */,
				34376F9322B0D434005130FF /* libz.tbd in Frameworks */,
				34376F9122B0D42D005130FF /* libc++.tbd in Frameworks */,
				8813F7534DCB5C30621DABC3 /* Pods_V_Key_Insights.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		34376F4F22B0BD28005130FF = {
			isa = PBXGroup;
			children = (
				342493022539A33300D45B40 /* activity_indicator.json */,
				3490829F2305068E00527289 /* Localizable.strings */,
				84A54BF426A6E7C50072A6D0 /* Assets */,
				84A54BE526A6E7A70072A6D0 /* Framework */,
				34376F5A22B0BD28005130FF /* SwiftTestAppVtap */,
				34376F5922B0BD28005130FF /* Products */,
				34376F8B22B0D291005130FF /* SwiftTestAppVtap-Bridging-Header.h */,
				34376F8F22B0D42C005130FF /* Frameworks */,
				57DD9B8F0A18EA1855410D1A /* Pods */,
			);
			sourceTree = "<group>";
		};
		34376F5922B0BD28005130FF /* Products */ = {
			isa = PBXGroup;
			children = (
				34376F5822B0BD28005130FF /* V-Key Insights.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		34376F5A22B0BD28005130FF /* SwiftTestAppVtap */ = {
			isa = PBXGroup;
			children = (
				8CBD2CB12E14D3CF005917DF /* ActivityIndicatorView.swift */,
				8CBD2CB22E14D3CF005917DF /* AppDelegate.swift */,
				8CBD2CB82E14D3CF005917DF /* ResultDetailViewController.swift */,
				8CBD2CB92E14D3CF005917DF /* UserGuideViewController.swift */,
				8CBD2CBA2E14D3CF005917DF /* ViewController.swift */,
				34376F9B22B0DD25005130FF /* Extensions */,
				34376F9422B0DCA1005130FF /* Utility */,
				34376F8A22B0D24F005130FF /* SwiftTestAppVtap.entitlements */,
				34376F5F22B0BD28005130FF /* Main.storyboard */,
				34376F6222B0BD2A005130FF /* Assets.xcassets */,
				34376F6422B0BD2A005130FF /* LaunchScreen.storyboard */,
				34376F6722B0BD2A005130FF /* Info.plist */,
				8C8BEF442BABE1080098CFF7 /* PrivacyInfo.xcprivacy */,
			);
			path = SwiftTestAppVtap;
			sourceTree = "<group>";
		};
		34376F8F22B0D42C005130FF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8CFD8A942B83599200DDD9A6 /* CoreLocation.framework */,
				34376F9722B0DCB5005130FF /* LocalAuthentication.framework */,
				34376F9222B0D434005130FF /* libz.tbd */,
				34376F9022B0D42D005130FF /* libc++.tbd */,
				F57704F5847910B6B4883821 /* Pods_V_Key_Insights.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		34376F9422B0DCA1005130FF /* Utility */ = {
			isa = PBXGroup;
			children = (
				343E1B9023040C580044669B /* ProgressBar.swift */,
				34376FA822B0DF4C005130FF /* Resource.swift */,
				34376F9922B0DCD5005130FF /* UserDefaultsHandle.swift */,
				34376F9522B0DCA1005130FF /* DeviceManager.swift */,
			);
			path = Utility;
			sourceTree = "<group>";
		};
		34376F9B22B0DD25005130FF /* Extensions */ = {
			isa = PBXGroup;
			children = (
				343E1B9223040CBA0044669B /* UIColor+Theme.swift */,
				34376F9C22B0DD25005130FF /* UIView+Toast.swift */,
				344E692C22B8F79800C24847 /* Data+HexString.swift */,
				34376F9D22B0DD25005130FF /* UIColor+Hex.swift */,
				34376F9F22B0DD25005130FF /* Observable+Extensions.swift */,
				34376FA022B0DD25005130FF /* UIViewController+ScrollForKeyboard.swift */,
				34376FA122B0DD25005130FF /* UIDevice+Platform.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		57DD9B8F0A18EA1855410D1A /* Pods */ = {
			isa = PBXGroup;
			children = (
				9018CC4CD5A6456E002860E7 /* Pods-V-Key Insights.debug.xcconfig */,
				2CCCA2ABC67969E8E37B8673 /* Pods-V-Key Insights.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		84A54BE526A6E7A70072A6D0 /* Framework */ = {
			isa = PBXGroup;
			children = (
				8C65BF8D2D898B270082BF30 /* VGuard.xcframework */,
				8C65BF8E2D898B270082BF30 /* VosWrapper.xcframework */,
			);
			name = Framework;
			path = SwiftTestAppVtap/Framework;
			sourceTree = "<group>";
		};
		84A54BF426A6E7C50072A6D0 /* Assets */ = {
			isa = PBXGroup;
			children = (
				84A54BF526A6E7C50072A6D0 /* firmware */,
				84A54BF626A6E7C50072A6D0 /* signature */,
				84A54BF826A6E7C50072A6D0 /* profile */,
				84A54BF926A6E7C50072A6D0 /* vkeylicensepack */,
			);
			name = Assets;
			path = SwiftTestAppVtap/Assets;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		34376F5722B0BD28005130FF /* V-Key Insights */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 34376F6A22B0BD2A005130FF /* Build configuration list for PBXNativeTarget "V-Key Insights" */;
			buildPhases = (
				D1B4C52D6F6EE2B8D065FDED /* [CP] Check Pods Manifest.lock */,
				34376F5422B0BD28005130FF /* Sources */,
				34376F5522B0BD28005130FF /* Frameworks */,
				34376F5622B0BD28005130FF /* Resources */,
				E2A2616F8271D89B2186B5F9 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "V-Key Insights";
			productName = SwiftTestAppVtap;
			productReference = 34376F5822B0BD28005130FF /* V-Key Insights.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		34376F5022B0BD28005130FF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1020;
				LastUpgradeCheck = 1250;
				ORGANIZATIONNAME = com.vkey;
				TargetAttributes = {
					34376F5722B0BD28005130FF = {
						CreatedOnToolsVersion = 10.2.1;
						LastSwiftMigration = 1020;
						SystemCapabilities = {
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 34376F5322B0BD28005130FF /* Build configuration list for PBXProject "SwiftTestAppVtap" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 34376F4F22B0BD28005130FF;
			productRefGroup = 34376F5922B0BD28005130FF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				34376F5722B0BD28005130FF /* V-Key Insights */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		34376F5622B0BD28005130FF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				84A54BFE26A6E7C50072A6D0 /* vkeylicensepack in Resources */,
				84A54BFD26A6E7C50072A6D0 /* profile in Resources */,
				34376F6622B0BD2A005130FF /* LaunchScreen.storyboard in Resources */,
				342493032539A33300D45B40 /* activity_indicator.json in Resources */,
				84A54BFB26A6E7C50072A6D0 /* signature in Resources */,
				3490829D2305068E00527289 /* Localizable.strings in Resources */,
				84A54BFA26A6E7C50072A6D0 /* firmware in Resources */,
				34376F6322B0BD2A005130FF /* Assets.xcassets in Resources */,
				8C8BEF452BABE1080098CFF7 /* PrivacyInfo.xcprivacy in Resources */,
				34376F6122B0BD28005130FF /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		D1B4C52D6F6EE2B8D065FDED /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-V-Key Insights-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E2A2616F8271D89B2186B5F9 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-V-Key Insights/Pods-V-Key Insights-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-V-Key Insights/Pods-V-Key Insights-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-V-Key Insights/Pods-V-Key Insights-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		34376F5422B0BD28005130FF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				344E692D22B8F79900C24847 /* Data+HexString.swift in Sources */,
				34376FA222B0DD25005130FF /* UIView+Toast.swift in Sources */,
				34376F9622B0DCA1005130FF /* DeviceManager.swift in Sources */,
				34376FA922B0DF4C005130FF /* Resource.swift in Sources */,
				34376FA322B0DD25005130FF /* UIColor+Hex.swift in Sources */,
				34376FA622B0DD25005130FF /* UIViewController+ScrollForKeyboard.swift in Sources */,
				34376F9A22B0DCD5005130FF /* UserDefaultsHandle.swift in Sources */,
				8CBD2CBB2E14D3CF005917DF /* UserGuideViewController.swift in Sources */,
				8CBD2CBD2E14D3CF005917DF /* ResultDetailViewController.swift in Sources */,
				8CBD2CBF2E14D3CF005917DF /* ActivityIndicatorView.swift in Sources */,
				8CBD2CC32E14D3CF005917DF /* ViewController.swift in Sources */,
				8CBD2CC42E14D3CF005917DF /* AppDelegate.swift in Sources */,
				343E1B9323040CBB0044669B /* UIColor+Theme.swift in Sources */,
				34376FA722B0DD25005130FF /* UIDevice+Platform.swift in Sources */,
				343E1B9123040C580044669B /* ProgressBar.swift in Sources */,
				34376FA522B0DD25005130FF /* Observable+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		34376F5F22B0BD28005130FF /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				349082992305054F00527289 /* Base */,
				8C7B6F042B8F1A13006E3C7B /* en */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		34376F6422B0BD2A005130FF /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				3490829A2305054F00527289 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		3490829F2305068E00527289 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				3490829E2305068E00527289 /* en */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		34376F6822B0BD2A005130FF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		34376F6922B0BD2A005130FF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		34376F6B22B0BD2A005130FF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9018CC4CD5A6456E002860E7 /* Pods-V-Key Insights.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SwiftTestAppVtap/SwiftTestAppVtap.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_TEAM = 63DA3SWC73;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/framework",
					"$(PROJECT_DIR)/SwiftTestAppVtap/Framework",
				);
				INFOPLIST_FILE = SwiftTestAppVtap/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "V-Key Insights";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.v-key.diagnosis";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "SwiftTestAppVtap-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		34376F6C22B0BD2A005130FF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2CCCA2ABC67969E8E37B8673 /* Pods-V-Key Insights.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SwiftTestAppVtap/SwiftTestAppVtap.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_TEAM = 63DA3SWC73;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/framework",
					"$(PROJECT_DIR)/SwiftTestAppVtap/Framework",
				);
				INFOPLIST_FILE = SwiftTestAppVtap/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "V-Key Insights";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.v-key.diagnosis";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "SwiftTestAppVtap-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		34376F5322B0BD28005130FF /* Build configuration list for PBXProject "SwiftTestAppVtap" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				34376F6822B0BD2A005130FF /* Debug */,
				34376F6922B0BD2A005130FF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		34376F6A22B0BD2A005130FF /* Build configuration list for PBXNativeTarget "V-Key Insights" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				34376F6B22B0BD2A005130FF /* Debug */,
				34376F6C22B0BD2A005130FF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 34376F5022B0BD28005130FF /* Project object */;
}
