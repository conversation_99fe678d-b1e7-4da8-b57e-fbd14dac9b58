//
//  UserGuideViewController.swift
//  VKey-Diagnosis
//
//  Created by ADMIN on 7/20/21.
//  Copyright © 2021 com.vkey. All rights reserved.
//

import UIKit

class UserGuideViewController: UIViewController {
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var contentLabel: UILabel!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupContent()
    }

    private func setupUI() {
        // Configure navigation
        navigationItem.title = "How to Use"
        navigationController?.navigationBar.prefersLargeTitles = false

        // Set purple background color
        view.backgroundColor = UIColor(red: 0.4549019608, green: 0.1960784314, blue: 0.58431372550000005, alpha: 1.0)

        // Setup scroll view
        if let scrollView = scrollView {
            scrollView.backgroundColor = UIColor(red: 0.4549019608, green: 0.1960784314, blue: 0.58431372550000005, alpha: 1.0)
            scrollView.contentInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
        }

        // Setup content label
        if let label = contentLabel {
            label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
            label.textColor = UIColor.white
            label.numberOfLines = 0
            label.lineBreakMode = .byWordWrapping
        }
    }

    private func setupContent() {
        let guideContent = """
        🔍 How to Perform a Security Scan:

        1. Start the Scan
           • Tap "Scan" on the main screen
           • The app will begin checking your device for threats
           • Wait for the scan to complete (this may take a few moments)

        2. Review Results
           • Once complete, check the status indicators:
             ✅ Green = Secure/Safe
             🚨 Red = Threats detected
             ⚠️ Yellow = Issues found
           • Tap "Results Detail " for more information

        📧 Sharing Results:

        • Send Report via Email: Creates an email with scan results
        • Share: Uses the system share to send files

        🛡️ What We Check:

        • System integrity and security status
        • Potential security vulnerabilities
        • Device environment safety
        • Application protection status

        ⚠️ Important Notes:

        • Keep your device updated with the latest security patches
        • Only install apps from trusted Apple store

        🔧 Troubleshooting:

        • If scan fails: Try restarting the app
        • For persistent issues: Check your device's security settings
        """

        contentLabel?.text = guideContent
    }

    @IBAction func back(_ sender: Any) {
        self.navigationController?.popViewController(animated: true)
    }
}
