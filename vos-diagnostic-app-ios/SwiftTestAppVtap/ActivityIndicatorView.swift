//
//  ActivityIndicatorView.swift
//  V-Key
/* show activity indicator of json file */
import Lottie
class ActivityIndicatorView: UIView{
    static let sharedInstance:ActivityIndicatorView = ActivityIndicatorView()
    var animationView: AnimationView?
    var animation: Animation?
    var currentView: UIView?
    var backgroundView: UIView?
    var containerView: UIView?
    var taskNameLabel: UILabel?
    
    // add animation to displayingView
    func playInMidleOfView(_ displayingView: UIView?, taskNameKey: String?){
        guard let view = displayingView else { return }
        if currentView != view {
            self.dismissSelf()
        }
        let selfBounds = view.bounds
        if containerView != nil { // showing, update task name
            // add text to taskNameLabel
            updateLabelText(taskNameLabel, taskNameKey: taskNameKey, selfBoundsWidth: selfBounds.size.width)
            return
        }
        currentView = view
        containerView = UIView()
        view.addSubview(containerView!)
        
        // add dark transferent view
        containerView?.frame = selfBounds
        backgroundView = UIView(frame: selfBounds)
        containerView!.addSubview(backgroundView!)
        backgroundView?.backgroundColor = UIColor.lightText
        backgroundView?.alpha = 0.3
        // add animation view
        animationView = AnimationView()
        /// Some time later
        animation = Animation.named("activity_indicator")
        animationView?.animation = animation
        animationView?.contentMode = .scaleToFill
        animationView?.loopMode = .loop // set loop status
        
        if let av = animationView {
            containerView!.addSubview(av)
        }
        // set size for animationView
        let size = CGSize(width: 150, height: 150)
        let animationFrame = CGRect(x: (selfBounds.size.width - size.width) / 2, y: (selfBounds.size.height - size.height) / 2, width: size.width, height: size.height)
        animationView?.frame = animationFrame
        // add task name
        let labelFrame = CGRect(x: 25, y: animationFrame.origin.y + animationFrame.size.height + 15, width: selfBounds.size.width - 50, height: 45)
        taskNameLabel = UILabel(frame: labelFrame)
//        Resource.sharedInstance.setColorFor(taskNameLabel, colorKey: "common_secondary_color", colorType: .backgroundColor)
//        Resource.sharedInstance.setColorFor(taskNameLabel, colorKey: "common_primary_color", colorType: .textColor)

        taskNameLabel?.numberOfLines = 0
        taskNameLabel?.textAlignment = .center
        taskNameLabel?.layer.shadowColor = UIColor.blue.cgColor
        taskNameLabel?.layer.masksToBounds = true
        taskNameLabel?.layer.cornerRadius = 8
        taskNameLabel?.layer.shadowOffset = CGSize(width: 2, height: 2)
        // add text to taskNameLabel
        updateLabelText(taskNameLabel, taskNameKey: taskNameKey, selfBoundsWidth: selfBounds.size.width)
        
        animationView?.play()
    }
    func updateLabelText(_ taskNameLabel: UILabel?, taskNameKey: String?, selfBoundsWidth: CGFloat){
        if let label = taskNameLabel {
            containerView?.addSubview(label)
            if let key = taskNameKey {
                let taskName = Resource.sharedInstance.getValueForKey(key)
                if taskName.count > 0 {
                    label.text = taskName
                    let yCoor = label.frame.origin.y
                    let maxWidth = selfBoundsWidth - 50
                    var minTextSize = label.intrinsicContentSize
                    if minTextSize.width > maxWidth {
                        let height = taskName.height(withConstrainedWidth: maxWidth, font: label.font)
                        minTextSize = CGSize(width: maxWidth, height: height)
                    }
                    minTextSize = CGSize(width: minTextSize.width + 22, height: minTextSize.height + 18)
                    label.frame = CGRect(x: (selfBoundsWidth - minTextSize.width) / 2, y: yCoor, width: minTextSize.width, height: minTextSize.height)
                    label.isHidden = false
                }else {
                    label.isHidden = true
                }
            }else {
                label.isHidden = true
                label.text = ""
            }
        }
    }
    func dismissSelf(){
        animationView?.stop()
        containerView?.removeFromSuperview()
        containerView = nil
        animationView = nil
        backgroundView = nil
        currentView = nil
        taskNameLabel = nil
    }
}

extension String {
    func height(withConstrainedWidth width: CGFloat, font: UIFont) -> CGFloat {
        let constraintRect = CGSize(width: width, height: .greatestFiniteMagnitude)
        let boundingBox = self.boundingRect(with: constraintRect, options: .usesLineFragmentOrigin, attributes: [.font: font], context: nil)
        
        return ceil(boundingBox.height)
    }
}
