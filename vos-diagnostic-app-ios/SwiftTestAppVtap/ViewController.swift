//
//  ViewController.swift
//  SwiftTestAppVtap
//
//  Created by Tu Do on 23/04/24.
//  Copyright © 2019 vkey.com All rights reserved.
//
import UIKit
import MessageUI // MFMailComposeViewController use
import Foundation
import CommonCrypto // copy and replace asset file base on sha256 value

func LCSTRING(_ str:String)->String{
    return NSLocalizedString(str, comment: "")
}

enum State {
    case Collect
    case Send
    case Collecting
}

let logFileName = "log.txt"
let vgFileName  = "vguard.db"
let vtFileName  = "vtap.db"
let vosFileName = "vos.log"

class ViewController: UIViewController, UITextFieldDelegate, VGuardThreatsDelegate, VGuardManagerProtocol, VGuardExceptionHandlerProtocol {
    
    @IBOutlet weak var scanBtn: UIButton!
    @IBOutlet weak var sendBtn: UIButton!
    @IBOutlet weak var shareBtn: UIButton!
    @IBOutlet weak var lb1: UILabel!
    @IBOutlet weak var TID_text: UITextField!
    @IBOutlet weak var infoView: UIView!

    
    var jsonThreatsArray : [[String: Any]]?
    var vTapStatus : Bool = false
    var pkiStatus : Bool = false
    var otpStatus : Bool = false
    
    var status_VOS : Int = -1
    var status_VGuard : Int = -1
    
    // declare VGuardManager
    var vGuardManager: VGuardManager? = nil
    
    // declare vGuardThreats
    var vGuardThreats: VGuardThreats? = nil
    private var state: State = .Collect
    var queue : OperationQueue?
    var ts = ""
    var aPin = ""
    var deviceInfo: String = ""
    var resultString_prepare: String = ""
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        setupTIDTextField()
        setupInitialState()
//        setupQueue()

        // Add observer for UIApplication.didBecomeActiveNotification
        NotificationCenter.default.addObserver(self, selector: #selector(appDidBecomeActive), name: UIApplication.didBecomeActiveNotification, object: nil)
    }

    private func setupUI() {
        // Configure navigation bar
        navigationController?.navigationBar.prefersLargeTitles = false
        navigationItem.title = "Device Security Check"
        
        // Add a button to navigate to the password manager
        let passwordManagerButton = UIBarButtonItem(title: "Passwords", style: .plain, target: self, action: #selector(openPasswordManager))
        passwordManagerButton.tintColor = .white
        navigationItem.rightBarButtonItem = passwordManagerButton

        // Set purple background color
        view.backgroundColor = UIColor(red: 0.4549019608, green: 0.1960784314, blue: 0.58431372550000005, alpha: 1.0)

        // Setup modern button styling
        setupModernButtons()

        // Setup info view
        setupInfoView()
    }
    
    // Method to open the password manager tab bar controller
    @objc func openPasswordManager() {
        // Create and present the password tab bar controller
        if let passwordTabBarController = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "password-tab-bar") as? UITabBarController {
            // Note: We're using present modally to keep the main view controller in the navigation stack
            // This allows users to return to the main screen easily
            navigationController?.present(passwordTabBarController, animated: true, completion: nil)
        }
    }

    private func setupTIDTextField() {
        // Set the delegate to self
        TID_text.delegate = self
        // Hide the caret by setting the tint color to clear
        TID_text.tintColor = .clear
        // Add tap gesture to copy text
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(copyTIDAction))
        TID_text.addGestureRecognizer(tapGesture)

        // Modern styling for TID field
        TID_text.backgroundColor = UIColor.systemGray
        TID_text.layer.cornerRadius = 8
        TID_text.font = UIFont.monospacedSystemFont(ofSize: 14, weight: .medium)
        TID_text.textColor = UIColor.label

        // Initially hide the TID field
        TID_text.isHidden = true
    }

    private func setupModernButtons() {
        let buttons = [scanBtn, sendBtn, shareBtn]

        for button in buttons {
            guard let btn = button else { continue }

            // Modern button styling
            btn.layer.cornerRadius = 12
            btn.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)

            // Add subtle shadow
            btn.layer.shadowColor = UIColor.black.cgColor
            btn.layer.shadowOffset = CGSize(width: 0, height: 2)
            btn.layer.shadowRadius = 4
            btn.layer.shadowOpacity = 0.1

            // Enable dynamic type
            btn.titleLabel?.adjustsFontForContentSizeCategory = true
        }

        // Primary button (Scan) - use system blue
        scanBtn.backgroundColor = UIColor.systemBlue
        scanBtn.setTitleColor(.white, for: .normal)

        // Secondary buttons - use system colors
        sendBtn.backgroundColor = UIColor.systemGreen
        sendBtn.setTitleColor(.white, for: .normal)

        shareBtn.backgroundColor = UIColor.systemOrange
        shareBtn.setTitleColor(.white, for: .normal)
    }



    private func setupInfoView() {
        // Modern info view styling
        infoView.backgroundColor = UIColor.secondarySystemBackground
        infoView.layer.cornerRadius = 12

        // Update label styling
        if let label = lb1 {
            label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
            label.textColor = UIColor.label
            label.numberOfLines = 0
        }
    }

    private func setupInitialState() {
        stateOfApp()
        updateButtonStates(scanEnabled: true, sendEnabled: false, shareEnabled: false)

        deviceInfo += "Model: " + UIDevice.modelName
        deviceInfo += "\n OS version: " + UIDevice.current.systemVersion
    }

    private func updateButtonStates(scanEnabled: Bool, sendEnabled: Bool, shareEnabled: Bool) {
        UIView.animate(withDuration: 0.3, animations: {
            self.scanBtn.isEnabled = scanEnabled
            self.sendBtn.isEnabled = sendEnabled
            self.shareBtn.isEnabled = shareEnabled

            // Update visual states with smooth animation
            self.scanBtn.alpha = scanEnabled ? 1.0 : 0.6
            self.sendBtn.alpha = sendEnabled ? 1.0 : 0.6
            self.shareBtn.alpha = shareEnabled ? 1.0 : 0.6

            // Add subtle scale animation for disabled state
            self.scanBtn.transform = scanEnabled ? .identity : CGAffineTransform(scaleX: 0.95, y: 0.95)
            self.sendBtn.transform = sendEnabled ? .identity : CGAffineTransform(scaleX: 0.95, y: 0.95)
            self.shareBtn.transform = shareEnabled ? .identity : CGAffineTransform(scaleX: 0.95, y: 0.95)
        })
    }

    private func animateButtonPress(_ button: UIButton) {
        UIView.animate(withDuration: 0.1, animations: {
            button.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                button.transform = .identity
            }
        }
    }
    
    // UITextFieldDelegate method to prevent editing
    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        return false
    }
    
    func requestClipboardAccess() {
        let alert = UIAlertController(title: "Clipboard Access",
                                      message: "Do you want to allow this app to access your clipboard?",
                                      preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "Allow", style: .default, handler: { _ in
            if UIPasteboard.general.hasStrings {
                let copiedText = UIPasteboard.general.string
                print("User allowed access: \(copiedText ?? "Empty")")
            }
        }))

        alert.addAction(UIAlertAction(title: "Deny", style: .cancel, handler: nil))

        if let topVC = UIApplication.shared.windows.first?.rootViewController {
            topVC.present(alert, animated: true, completion: nil)
        }
    }
    
    @objc func copyTIDAction() {
        if let text = TID_text.text, text.hasPrefix("TID: ") {
            let tidValue = String(text.dropFirst(5)) // Extracting only TID
            if !tidValue.isEmpty && tidValue.lowercased() != "n/a" {
                DispatchQueue.main.async {
                    UIPasteboard.general.string = tidValue
                }
                showSuccessToast(message: "📋 TID copied to clipboard!")

                // Add visual feedback with brief highlight
                UIView.animate(withDuration: 0.1, animations: {
                    self.TID_text.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.3)
                }) { _ in
                    UIView.animate(withDuration: 0.3) {
                        self.TID_text.backgroundColor = UIColor.systemGray
                    }
                }
            } else {
                showErrorToast(message: "No TID available to copy")
            }
        }
    }
    
    func showToast(message: String) {
        showToast(message: message, backgroundColor: UIColor.label.withAlphaComponent(0.8))
    }

    func showSuccessToast(message: String) {
        showToast(message: message, backgroundColor: UIColor.systemGreen.withAlphaComponent(0.9))
    }

    func showErrorToast(message: String) {
        showToast(message: message, backgroundColor: UIColor.systemRed.withAlphaComponent(0.9))
    }

    private func showToast(message: String, backgroundColor: UIColor) {
        let toastLabel = UILabel()
        toastLabel.backgroundColor = backgroundColor
        toastLabel.textColor = UIColor.white
        toastLabel.textAlignment = .center
        toastLabel.text = message
        toastLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        toastLabel.alpha = 0.0
        toastLabel.layer.cornerRadius = 12
        toastLabel.clipsToBounds = true
        toastLabel.numberOfLines = 0

        // Calculate size based on content
        let maxSize = CGSize(width: view.frame.width - 40, height: CGFloat.greatestFiniteMagnitude)
        let size = toastLabel.sizeThatFits(maxSize)
        let padding: CGFloat = 16

        toastLabel.frame = CGRect(
            x: (view.frame.width - size.width - padding * 2) / 2,
            y: view.safeAreaInsets.top + 20,
            width: size.width + padding * 2,
            height: size.height + padding
        )

        view.addSubview(toastLabel)

        // Animate in
        UIView.animate(withDuration: 0.3, animations: {
            toastLabel.alpha = 1.0
        }) { _ in
            // Animate out after delay
            UIView.animate(withDuration: 0.3, delay: 2.0, options: .curveEaseOut, animations: {
                toastLabel.alpha = 0.0
            }) { _ in
                toastLabel.removeFromSuperview()
            }
        }
    }
    
    // Function to handle when app becomes active
    @objc func appDidBecomeActive() {
        configureVGuard_and_start()
    }
    
    deinit {
        // Remove observer when the view controller is deinitialized
        NotificationCenter.default.removeObserver(self, name: UIApplication.didBecomeActiveNotification, object: nil)
    }
    
    @IBAction func showResultDetailVC(_ sender: Any) {
        if let resultDetailVC = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "ResultDetailViewController") as? ResultDetailViewController,
           let navigationController = self.navigationController {
            // Set the scan results and status information
            resultDetailVC.resultString_prepare = resultString_prepare
            resultDetailVC.updateStatus(systemStatus: status_VOS, securityStatus: status_VGuard)

            DispatchQueue.main.async {
                navigationController.pushViewController(resultDetailVC, animated: true)
            }
        }
    }
    
    @IBAction func showUserGuideVC(_ sender: Any) {
        if let userGuideVC = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "UserGuideViewController") as? UserGuideViewController,
           let navigationController = self.navigationController {
            DispatchQueue.main.async {
                navigationController.pushViewController(userGuideVC, animated: true)
            }
        }
    }
    
    override func prepare(for segue: UIStoryboardSegue, sender: Any?) {
        if segue.identifier == "ResultDetailViewController",
           let resultDetailVC = segue.destination as? ResultDetailViewController {
            resultDetailVC.resultString_prepare = resultString_prepare
        }
    }
    
    func vGuardDidDetectSSLError(_ error: Error) {
        print("vGuardDidDetectSSLError")
    }
    
    func vGuardDidDetectScreenSharing() {
        print("^vGuardDidDetectScreenSharing")
//        showAlert(title: "Warning", message: "ScreenSharing Detected")
    }
    
    func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        let okAction = UIAlertAction(title: "OK", style: .default) { _ in
            alert.dismiss(animated: true, completion: nil)
        }
        alert.addAction(okAction)
        self.present(alert, animated: true, completion: nil)
    }
    
    func statusVOS(_ status: VOS_STATUS, withError error: Error?) {
        DispatchQueue.main.async {
            // Show TID field and update with troubleshooting ID
            self.TID_text.isHidden = false
            let vTid = self.vGuardManager?.getTroubleshootingId()
            self.TID_text.text = "TID: " + (vTid ?? "N/A")

            // Add smooth animation when showing
            self.TID_text.alpha = 0
            UIView.animate(withDuration: 0.3) {
                self.TID_text.alpha = 1
            }
        }
        print("statusVOS: \((status == VOS_OK) ? "VOS_OK" : "VOS_NOTOK") - error: \(String(describing: error))")
        if let _error = error as NSError? {
            self.status_VOS = _error.code
        } else {
            self.status_VOS = Int(VOS_OK.rawValue)
        }
    }
    
    func statusVGuard(_ status: VGUARD_STATUS, withError error: Error?) {
        let statusString = (status == VGUARD_SAFE) ? "VGUARD_SAFE" : (status == VGUARD_UNSAFE) ? "VGUARD_UNSAFE" : "VGUARD_UNDETERMIND";
        print("statusVGuard: \(statusString) - error: \(String(describing: error))");
        
        if let _error = error as NSError? {
            self.status_VGuard = _error.code
        } else {
            self.status_VGuard = Int(status.rawValue)
        }
    }
    
    func vGuardDidFinishInitializing(_ status: Bool, withError error: Error?) {
        print("vGuardDidFinishInitializing: \(status ? "SUCCESS" : "FAILED") - Error: \(String(describing: error))");
        DispatchQueue.main.async { [unowned self] in
            updateButtonStates(scanEnabled: true, sendEnabled: true, shareEnabled: true)

            self.scanBtn.setTitle("Re-Scan", for: .normal)
            ViewController.hideActivityIndicatorView()

            // Show completion feedback
            if status {
                showSuccessToast(message: "Scan completed successfully!")
            } else {
                showErrorToast(message: "Scan completed with issues")
            }
        }
    }
    
    func sslErroDetected(_ error: Error!) {
        print("sslErroDetected: \(String(describing: error))");
    }
    
    func vGuardExceptionHandler(_ exception: NSException!) {
        print("vGuardExceptionHandler: \(String(describing: exception))");
    }
    

    
    func setupQueue(){
        queue = OperationQueue()
        queue?.name = "VOS Queue"
        queue?.qualityOfService = .default
        queue?.maxConcurrentOperationCount = 1
    }
    
    @available(iOS 13.4, *)
    func checkVKeyAssetsAndReplace() {
        let fileNames = ["firmware", "signature", "vkeylicensepack", "profile", "voscodesign.vky"]
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.path else {
            print("^Error: Unable to access documents directory")
            return
        }

        for fileName in fileNames {
            let docFilePath = (documentsPath as NSString).appendingPathComponent(fileName)
            let bundleFilePath = Bundle.main.path(forResource: fileName, ofType: nil)
            
            let docSha256 = sha256HashOfFile(atPath: docFilePath)
            let bundleSha256 = sha256HashOfFile(atPath: bundleFilePath ?? "")
            
            print("^Checking file: \(fileName)")
            print("^Document SHA-256: \(docSha256 ?? "nil")")
            print("^Bundle SHA-256:   \(bundleSha256 ?? "nil")")
            
            if let docSha256 = docSha256, let bundleSha256 = bundleSha256, docSha256 != bundleSha256, let bundleFilePath = bundleFilePath {
                do {
                    if FileManager.default.fileExists(atPath: docFilePath) {
                        try FileManager.default.removeItem(atPath: docFilePath)
                    }
                    try FileManager.default.copyItem(atPath: bundleFilePath, toPath: docFilePath)
                    print("^replace: \(fileName)")
                } catch {
                    print("^Error replacing file \(fileName): \(error)")
                }
            }
        }
    }

    private func sha256HashOfFile(atPath path: String) -> String? {
        guard let fileHandle = FileHandle(forReadingAtPath: path) else { return nil }
        defer { fileHandle.closeFile() }
        
        var context = CC_SHA256_CTX()
        CC_SHA256_Init(&context)
        
        while autoreleasepool(invoking: {
            let data = fileHandle.readData(ofLength: 1024 * 1024)
            if !data.isEmpty {
                data.withUnsafeBytes { buffer in
                    _ = CC_SHA256_Update(&context, buffer.baseAddress, CC_LONG(buffer.count))
                }
                return true
            }
            return false
        }) {}
        
        var digest = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        CC_SHA256_Final(&digest, &context)
        
        return digest.map { String(format: "%02hhx", $0) }.joined()
    }
    
//    func configureVGuard() {
//        if #available(iOS 13.4, *) {checkVKeyAssetsAndReplace()}
    
    func configureVGuard_and_start() {
        if (vGuardManager != nil) {
            vGuardManager?.start()
        } else {
            DispatchQueue.global(qos: .userInitiated).async { [self] in // Create a high priority bg thread
                vGuardManager = VGuardManager.shared()
                vGuardManager!.delegate = self
                vGuardManager!.setMemoryConfiguration(VOSMemoryConfiguration(rawValue: 1)!)
                //                vGuardManager!.isDebug = true // only trigger when do debug
                
                // VirtualTapDetection configuration
                vGuardThreats?.addVirtualTapDetection(self.view)
                
                vGuardManager?.setThreatIntelligenceServerURL("https://78-ti.cloud.v-key.com")
                VosWrapper.setLoggerBaseUrl("https://78-ti.cloud.v-key.com")
                
                VGuardExceptionHandler.sharedManager()?.delegate = self
                VGuardExceptionHandler.sharedManager().allowSendStacktraceLog = false
                
                VGuardThreats.sharedModule()?.delegate = self
                
                vGuardManager?.initializeVGuard()
                vGuardManager?.start()
            }
        }
    }
    
    private func setupVguard() {
        configureVGuard_and_start()
    }
    
    func stateOfApp() {
        DispatchQueue.main.async {
            switch self.state {
            case .Collect:
                self.infoView.isHidden = false
                self.lb1.text = LCSTRING("App_description")+"\n\n"+LCSTRING("App_note");
            case .Collecting:
                break
            case .Send:
                var descript = ""
                if let _jsonThreatsArray = self.jsonThreatsArray {
                    if(_jsonThreatsArray.count  > 0){
                        descript = LCSTRING("scan_error")
                        descript = descript.replacingOccurrences(of: "%threats%", with: _jsonThreatsArray.description)
                        
                    } else {
                        descript = LCSTRING("scan_success")
                    }
                } else {
                    descript = LCSTRING("scan_success")
                }
                self.lb1.text = descript+"\n\n"+LCSTRING("send_description")

                self.infoView.isHidden = false
                //                self.scanBtn.setTitle("Send", for: .normal)
            }
        }
    }
    
    @IBAction func press(_ sender: Any) {
        if let button = sender as? UIButton {
            animateButtonPress(button)
        }
        resultString_prepare = ""
        startScan()
    }

    @IBAction func send(_ sender: Any) {
        if let button = sender as? UIButton {
            animateButtonPress(button)
        }
        sendEmail()
    }

    @IBAction func share(_ sender: Any) {
        if let button = sender as? UIButton {
            animateButtonPress(button)
        }
        shareFiles()
    }

    private func startScan() {
        // Update UI for scanning state
        updateButtonStates(scanEnabled: false, sendEnabled: false, shareEnabled: false)

        UIView.animate(withDuration: 0.3) {
            self.scanBtn.setTitle("Scanning...", for: .normal)
        }

        // Start the actual scan
        collectLog()
    }

    private func shareFiles() {
        let fileManager = FileManager.default
        let docDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]

        // Create array to store files to share
        var filesToShare = [Any]()

        // List of files to share
        let files = [
            logFileName,
            vgFileName,
            vtFileName,
            vosFileName
        ]

        // Add existing files to share array
        for filename in files {
            let filePath = docDirectory.appendingPathComponent(filename)
            if fileManager.fileExists(atPath: filePath.path) {
                filesToShare.append(filePath)
            }
        }

        // Show share sheet if there are files to share
        if !filesToShare.isEmpty {
            let activityViewController = UIActivityViewController(activityItems: filesToShare, applicationActivities: nil)
            self.present(activityViewController, animated: true, completion: nil)
        } else {
            showErrorToast(message: "No files available to share")
        }
    }
    


    
    func collectLog() {
        ViewController.showActivityIndicatorView("Loading")
        setupVguard()
    }
    
    // MARK: VGuardThreatsDelegate
    func vGuardScan(_ threatsArray: [Any]!) {
        resultString_prepare = ""
        print("vGuardScan: \(String(describing: threatsArray))")

        guard !threatsArray.isEmpty else {
            resultString_prepare += "✅ Excellent! Your device is clean.\n. Your device appears to be running in a trusted environment."
            DispatchQueue.main.async {
                self.showSuccessToast(message: "Device is secure!")
            }
            return
        }

        jsonThreatsArray = nil
        guard let jsonThreats = threatsArray as? [[String: Any]] else {
            return
        }

        if jsonThreats.count > 0 {
            jsonThreatsArray = jsonThreats
            let threatCount = jsonThreats.count
            let threatWord = threatCount == 1 ? "threat" : "threats"

            resultString_prepare += "🚨 Security Alert\n\nWe detected \(threatCount) security \(threatWord) on your device:\n\n"

            // Process each threat and add details
            for (index, threat) in jsonThreats.enumerated() {
                if let threatClass = threat["threatTypeId"],
                   let threatInfo = threat["info"] as? String,
                   let threatName = threat["name"] {

                    resultString_prepare += "⚠️ Threat \(index + 1):\n"
                    resultString_prepare += "• Name: \(threatName)\n"
                    resultString_prepare += "• Type: \(threatClass)\n"
                    resultString_prepare += "• Details: \(threatInfo)\n\n"
                }
            }

            resultString_prepare += "📧 Need Help?\n"
            resultString_prepare += "Tap 'Send Report via Email' to send troubleshooting logs to V-Key support for analysis and assistance."

            DispatchQueue.main.async {
                self.showErrorToast(message: "\(threatCount) security \(threatWord) found")
            }
        }
    }
}

extension ViewController : MFMailComposeViewControllerDelegate{
    func sendEmail() {
        let fileManager = FileManager.default
        let docDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        
        if !MFMailComposeViewController.canSendMail() {
            print("Mail services are not available")
            return
        }
        
        let date = Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM/yy HH:mm:ss Z"
        formatter.timeZone = TimeZone.current
        let dateCrr = formatter.string(from: date)
        
        let composeVC = MFMailComposeViewController()
        composeVC.mailComposeDelegate = self
        composeVC.setToRecipients(["<EMAIL>"])
        composeVC.setSubject("iOS V-Guard scan results on " + dateCrr)
        composeVC.setMessageBody(deviceInfo, isHTML: false)
        
        // List of files to attach
        let filesToAttach = [
            (logFileName, "text/plain"),
            (vgFileName, "application/x-sqlite3"),
            (vtFileName, "application/x-sqlite3"),
            (vosFileName, "text/plain")
        ]
        
        // Attach each file if it exists
        for (filename, mimeType) in filesToAttach {
            let filePath = docDirectory.appendingPathComponent(filename)
            var isDirectory: ObjCBool = false
            
            if fileManager.fileExists(atPath: filePath.path, isDirectory: &isDirectory) {
                do {
                    let fileData = try Data(contentsOf: filePath)
                    composeVC.addAttachmentData(fileData, mimeType: mimeType, fileName: filename)
                } catch {
                    print("Error attaching file \(filename): \(error.localizedDescription)")
                }
            }
        }
        self.present(composeVC, animated: true, completion: nil)
    }
    
    func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
        controller.dismiss(animated: true, completion: nil)
    }
    
    static func showActivityIndicatorView(_ taskNamekey: String?){
          DispatchQueue.main.async {
            if let keyWindow = UIApplication.shared.windows.filter({$0.isKeyWindow}).first
            {
//              if let keyWindow = RouterManager.getAppDelegateKeyWindow(){
                  CATransaction.begin() // avoid conflict navigation aninmation
                  ActivityIndicatorView.sharedInstance.playInMidleOfView(keyWindow, taskNameKey: taskNamekey)
                  CATransaction.commit()
              }
          }
      }
      static func hideActivityIndicatorView(){
          DispatchQueue.main.async {
              CATransaction.begin() // avoid conflict navigation aninmation
              ActivityIndicatorView.sharedInstance.dismissSelf()
              CATransaction.commit()
          }
      }
}
