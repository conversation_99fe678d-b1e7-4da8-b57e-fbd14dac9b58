//
//  DeviceManager.swift
//  V-Key
//
//  Created by <PERSON> on 5/17/19.
//  Copyright © 2019 VKey. All rights reserved.
//
// handle device token
import LocalAuthentication
class DeviceManager{
    struct DeviceKeys{
        static let deviceToken = "com.vkey.cloud.vkey_store_key_device_token"
        static let deviceChecked = "com.vkey.cloud.device_checked"
        static let runFromRemote = "com.vkey.cloud.run_from_remote_notify"
        static let hadPINAlready = "com.vkey.cloud.had_pin"
        static let activatedAtLeast1Time = "activated_at_least_1Time"
    }
    static func setRunFromRemoteNotification(_ status: Bool?){
        UserDefaultsHandle.sharedInstance.saveToLocal(status, forKey: DeviceKeys.runFromRemote)
    }
    
    static func isRunFromRemoteNotification()->Bool?{
        return UserDefaultsHandle.sharedInstance.getLocalForKey(DeviceKeys.runFromRemote) as? Bool
    }
    // device token is used for push notification feature
    static func setDeviceToken(_ deviceToken: String?) {
        UserDefaultsHandle.sharedInstance.saveToLocal(deviceToken, forKey: DeviceKeys.deviceToken)
    }
    
    static func getDeviceToken() -> String? {
        if let deviceToken = UserDefaultsHandle.sharedInstance.getLocalForKey(DeviceKeys.deviceToken) as? String {
            return deviceToken
        }
        return nil
    }
    
    static func setDeviceChecked(_ status: Bool) {
        UserDefaultsHandle.sharedInstance.saveToLocal(status, forKey: DeviceKeys.deviceChecked)
    }
    
    static func isDeviceChecked() -> Bool {
        if let deviceChecked = UserDefaultsHandle.sharedInstance.getLocalForKey(DeviceKeys.deviceChecked) as? Bool {
            return deviceChecked
        }
        return false
    }
    static func getDeviceId()-> String{
        if let deviceId = UIDevice.current.identifierForVendor?.uuidString {
            return deviceId
        }
        
        return ""
    }
    static func checkBioVerify(_ _success: @escaping () -> Void, error _error: @escaping (_ errorMessage: String?) -> Void) {
        let context = LAContext()
        let canEvaluatePolicy = context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: nil)
        if canEvaluatePolicy {
//            context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: Resource.sharedInstance.getValueForKey(Keys.biometricCheck), reply: { success, error in
//                if success {
//                    _success()
//                } else {
//                    _error(Resource.sharedInstance.getValueForKey(Keys.biometricCheckError))
//                }
//            })
        } else {
//            _error(Resource.sharedInstance.getValueForKey(Keys.bioometricLocked))
        }
    }
    // save PIN for the first time setting pin
    static func getHasPINAlready()->String?{
        if let pin = UserDefaultsHandle.sharedInstance.getLocalForKey(DeviceKeys.hadPINAlready) as? String {
            return pin
        }
        return nil
    }
    static func setHasPINAlready(_ pin: String?) {
        UserDefaultsHandle.sharedInstance.saveToLocal(pin, forKey: DeviceKeys.hadPINAlready)
    }
    // make sure that the user activated at least 1 time,
    // when user open app again, if there is added account in account list, even it is disable, app will open home screen in WelcomeViewController.swift file
    static func setActivatedAtLeast1Time(_ activated: Bool?) {
        UserDefaultsHandle.sharedInstance.saveToLocal(activated, forKey: DeviceKeys.activatedAtLeast1Time)
    }
    static func getActivatedAtLeast1Time() -> Bool? {
        if let b = UserDefaultsHandle.sharedInstance.getLocalForKey(DeviceKeys.activatedAtLeast1Time) as? Bool{
            return b
        }
        return false
    }
    
    static func getPlatform()->String{
        var platform = "iOS"
        let modelName = UIDevice.modelName
        if modelName.count > 0{
            platform += "/" + modelName
        }
        return platform
    }
}
