//
//  Resource.swift
//  V-Key
//
//  Created by <PERSON> on 5/13/19.
//  Copyright © 2019 V-Key. All rights reserved.
//

/* Use for changing theme which depends on each client */
let iosSuportVersion = 9
public func print(_ items: Any..., separator: String = " ", terminator: String = "\n"){
    #if DEBUG
    let output = items.map { "----DEBUG:^\($0)" }.joined(separator: separator)
        Swift.print(output, terminator: terminator)    
    #endif
}

struct HexColors{
    static let  navigationTitle = "#613384FF",
    pink = "#F7F2FEFF",
    button = "#2DBEC2FF",
    placeHolder = "#669CBFFF",
    homeTableCellColor = "#1072BDFF",
    homeSmallCircleColor = "#00FF00FF",
    homeOuterCircleColor = "#EAF0FFFF",
    welcomeTextColor = "#FFFFFFFF",
    welcomeFirstBGColor = "#5F2685FF",
    welcomeSecondBGColor = "#2AB4B7FF",
    welcomeProtectColor = "#392645FF",
    welcomeApproveColor = "#114B4DFF",
    
    settingsTitleColor = "#76D6FFFF",
    settingsSeparatorLine = "#EDEDEDFF",
    tokenListNameColor = "#82C5FBFF",
    tableviewDeleteButtonColor = "#82C5FBFF",
    tableviewViewHistoryButtonColor = "#309BF8FF",
    
    authenticationHistoryRowText = "#615069FF",
    authenticationHistoryThreadText = "#58B0F9FF",
    authenticationHistoryThreadBackground = "#F2F9FFFF",
    
    // account list vc
    accountHighlighted = "#FB8724FF",
    addAccountBackGround = "#E9ECF2FF",
    accountCellBorder = "#C5C8CCFF"
}

//struct MyError: Error {
//    var localizedDescription: String?
//}
// soft token
struct ServerKeys{
    static let progressKey = "progess"
    static let didStartVTap  = "didStartVTap"
    static let pushRegister = "pushNotificationRegister"
    static let dataKey = "data"
    static let checkDeviceCompatibility = "checkDeviceCompatibility"
    static let getLoadAckTokenFirmware = "getLoadAckTokenFirmware"
    static let createPin = "createPin"
    static let checkPin = "checkPin"
    static let generateCSR0 = "generateCsrAndSend_0"
    static let pkiDownload0 = "pkiCertDownload_0"
    static let generateCsr1 = "generateCsrAndSend_1"
    static let pkiDownload1 = "pkiCertDownload_1"
    static let pushAps = "aps"
    static let pushAlert = "alert"
    static let pushAction = "action"
}
// DistinguishedName
struct DNConstants {
    static let country  = "SG"
    static let stateName = "SG"
    static let localityName = "SG"
    static let organizationName = "V-Key"
    static let organizationUnit = "V-Key Staff"
}
struct Cert{
    static let authCert = "AUTH"
    static let aspCert = "ASP_CERT"
    static let smpCert = "SMP_CERT"
}

struct PushNotiMessageKeys{
    static let messageId = "messageId"
    static let type = "messageType"
    static let flag = "msgFlag"
    static let passType = "passType"
    static let passtypeEncrypt = "2"
    static let messageTitle = "title"
    static let appMessageKey = "appMessage"
    static let displayMessage = "displayMessage"
    static let messageList = "pushNotiMessageList"
    static let messageKey = "pushNotiMessage"
    static let messageAck = "pushNotiMessageAck"
    static let message = "notifyMsg"
    static let dataTobeSigned = "dataToBeSigned"
    static let authRequest = "authRequest"
    static let updateIndicatorOfPushNoti = "updateIndicatorOfPushNoti"
}
struct Pin {
    static let tempPin = "111111"
    static let pinLength = 6
    static let verificationMethodPIN: Int32 = 10001
}

struct Keys{
    static let success = "success"
    // welcome screen
    static let notSupportIOSTitle = "not_support_ios_title"
    static let notSupportIOSMessage = "not_support_ios_message"
    static let welcomeLabel = "welcome_label"
    static let welcomeProtect = "welcome_protect"
    static let welcomeProtectBold = "welcome_protect_bold"
    static let welcomeApprove = "welcome_approve"
    static let welcomeApproveBold = "welcome_approve_bold"
    static let welcomeEnable = "welcome_enable"
    static let welcomeEnableBold = "welcome_enable_bold"
    static let welcomeTextView = "welcome_text"
    static let errorTitle = "error_title"
    static let notificationTitle = "Notification"
    static let tokenNotAvailable = "token_not_available"
    static let alertTitle = "alert_title"
    static let smthErrorTitle = "smt_error_title"
    static let splashGreyListDialogPrompt = "splash_grey_list_dialog_prompt"
    static let splashSendDeviceInfoFailedDialogBody = "splash_send_device_info_failed_dialog_body"
    static let splashBlackListDialogBody = "splash_black_list_dialog_body"
    static let ok = "ok"
    static let getOnBoard = "get_on_board"
    static let next = "next"
    static let skip = "skip"
    // information screen
    static let informationVCTitle = "information_vc_title"
    static let termOfUser = "term_of_user"
    static let privacyPolicy = "privacy_policy"
    // introduction screen
    static let introductionVCTitle = "introduction_vc_title"
    static let introduceText = "introduce_text"
    static let pointQrCodeLabel = "point_qr_code_label"
    static let signInInformation = "sign_in_information"
    // Scanning screen
    static let scanningQRCodeVCTitle = "scanning_qr_code_vc_title"
    static let sanningNotSupportTitle = "sanning_not_support_title"
    static let sanningNotSupportMessage = "sanning_not_support_message"
    // sign in screen
    static let userName = "user_name"
    static let password = "password"
    // activating screen
    static let vtapFailedMessage = "vtap_failed_message"
    static let interupt = "interupt"
    static let splashVtapSetupFailedDialogBody = "splash_vtap_setup_failed_dialog_body"
    static let splashDeviceCheckFailedDialogBody = "splash_device_check_failed_dialog_body"
    static let accountLocked = "account_locked"
    static let registerFail = "register_fail"
    static let pushRegister = "push_register"
    static let registerAuthentication = "register_authentication"
    static let pkiAuthenticationFail = "pki_authentication_fail"
    static let apnError = "apn_error"
    static let vtapErrorConnection = "vtap_error_connection"
    static let alertPowerOn = "alert_power_on"
    static let registerVMmessage = "register_v_message"
    static let createTokenPINFailed = "createTokenPIN_failed"
    static let authenticationRequestDesc = "authentication_request_desc"
    // activating token
    static let activatingTokenInformation = "activating_token_information"
    // set pin screen
    static let settingPINTitle = "setting_pin_title"
    static let confirmPINTitle = "confirm_pin_title"
    static let changingPINTitle = "changing_pin_title"
    static let setPin = "set_pin"
    static let confirmPin = "confirm_pin"
    static let oldPin = "old_pin"
    static let newPin = "new_pin"
    static let confirmNewPin = "confirm_new_pin"
    static let settingPinInformation = "setting_pin_information"
    static let pinAlertLength = "pin_alert_lenght"
    static let pinInvalidLength = "pin_invalid_length"
    static let updatePINFail = "update_pass_fail"
    static let setPINFail = "set_pin_fail"
    static let changePINFail = "change_pin_fail"
    static let confirmPinNotMatch = "confirm_pin_not_match"
    static let confirmNewPinIsSame = "confirm_new_pin_is_same"
    static let pinIncorrect = "pin_incorrect"
    static let oldPINIncorrect = "old_pin_incorrect"
    // Push notification
    static let pushNotificationTitleVNP = "push_notification_title_vpn"
    static let pushNotificationTitleO365 = "push_notification_title_o365"
    static let pushNotificationDescVNP = "push_notification_desc_vpn"
    static let pushNotificationDescO365 = "push_notification_desc_o365"
    static let authenticationFailed = "authentication_failded"
    static let biometricCheckError = "biometric_check_error"
    static let biometricCheck = "biometric_check"
    static let bioometricLocked = "biometric_locked"
    static let alertPushNotiSwitchAccountTitle = "alert_switch_account_title"
    static let alertPushNotiSwitchAccountMessage = "alert_switch_account_message"
    // Home screen
    static let appName1 = "app_name_1"
    static let appName2 = "app_name_2"
    static let appName3 = "app_name_3"
    // Account screen
    static let switchAccountMessage = "switch_account_message"
    static let service = "service"
    static let platform = "platform"
    static let date = "date"
    static let location = "location"
    static let delete = "delete"
    static let viewHistory = "view_history"
    static let askDeleteAccountTitle = "ask_delete_account_title"
    static let tokenScreenTitle = "token_screen_title"
    static let loadMore = "load_more"
    static let pullToRefresh = "pull_to_refresh"
    // setting screen
    static let myTokenTitle = "my_token_title"
    static let profileSection = "profile_section"
    static let settingsSection = "setting_section"
    static let informationSection = "information_section"
    static let email = "email"
    static let enablePin = "enable_pin"
    static let changePin = "change_pin"
    static let enableFingerprint = "enable_fingerprint"
    static let enableFaceId = "Eenable_face_id"
    static let removeReset = "remove_reset"
    static let transactions = "transactions"
    static let about = "about"
    static let helps = "helps"
    static let removeAccountConfirm = "remove_account_confirm"
    static let removeAccountFailed = "remove_account_failed"
    static let setPINSuccessful = "set_pin_successful"
    static let changePINSuccessful = "change_pin_successful"
    static let deleteAccountSuccessful = "delete_account_successful"
    static let resetAccountSuccessful = "reset_account_successful"
}
struct ImageNameKeys{
    static let pullToRefresh = "pull_to_refresh_image_name"
    static let gridIcon = "grid_icon"
}
struct SVGFileNames{
    static let backArrow = "back_arrow.svg"
    static let setting = "setting.svg"
    static let scanFrame = "scan_frame.svg"
    static let camera = "camera.svg"
    static let downArrowCollapse = "down_arrow_collapse.svg"
    static let upArrowCollapse = "up_arrow_collapse.svg"
    static let infoIcon = "information.svg"
    static let profileIcon = "profile.svg"
    static let settingsIcon = "setting_cicular.svg"
    static let addIcon = "add.svg"
    static let vpnIcon = "vpn_icon.svg"
    static let o365Icon = "o365_icon.svg"
    static let doorIcon = "door_icon.svg"
    static let gridIcon = "grid_icon.svg"
}
struct ImageNames{
    static let information = "info"
    static let scan = "scan"
    static let next = "next"
}

class Resource {
    static let sharedInstance = Resource()
    
    var keysValues:[String : String] = [
        Keys.success: "Success",
        Keys.notSupportIOSTitle: "Not support",
        Keys.notSupportIOSMessage: "Only support from iOS 9",
        Keys.welcomeLabel: "Welcome to V-OS Smart Token App",
        Keys.welcomeProtect: "Protect your organization's services with security functionalities",
        Keys.welcomeProtectBold: "Protect",
        Keys.welcomeApprove: "Approve the login attempts to the services enabled by your organization.",
        Keys.welcomeApproveBold: "Approve",
        Keys.welcomeEnable: "To enable login authentication for the services, you need to have the QR code given to your organizationn and the account enabled by your system admin. Contact your system admin for assistance, if necessary.",
        Keys.welcomeEnableBold: "QR code",
        Keys.welcomeTextView: "V-Key White-Label App helps you protect your organization's services with security functionalities\n\nYou can use this app to approve login attemps to the services that are enabled by your organization. Such services can be VPN client login, Office 365 web/app login, and more.\n\nTo enable login authentication for the services, you need to have the QR code given to your Organizationn and the account enabled by your system admin. Contact your system admin for assistance, if necessary.\n\nTap NEXT to start the onboarding process.",
        Keys.errorTitle: "Error",
        Keys.tokenNotAvailable: "Device token isn't received. Please wait until device receive token and try again.",
        Keys.alertTitle: "Alert",
        Keys.splashGreyListDialogPrompt: "You are using a greylisted device. Your data might be at risk. Do you still want to proceed?",
        Keys.splashSendDeviceInfoFailedDialogBody: "Failed to send device information to the server.",
        Keys.splashBlackListDialogBody: "V-OS Smart Token does not support your device. Please quit the App.",
        Keys.ok: "OK",
        Keys.getOnBoard: "GET ONBOARD",
        Keys.next: "NEXT",
        Keys.skip: "SKIP",
        // information screen
        Keys.informationVCTitle: "Information",
        Keys.termOfUser: "Term of User",
        Keys.privacyPolicy: "Privacy Policy",
        // Introduction screen
        Keys.introductionVCTitle: "Scanning QR code",
        Keys.introduceText: "Token packs are added by scanning a token QR code that is assigned to your organization. " +
        "Contact your admin if you don't have this code.",
        Keys.pointQrCodeLabel: "Point the camera at the QR code",
        Keys.signInInformation: "Enter your user name and password. This account is provided by your admin. This account is also the account to use with your services",
        // Scanning QR Code VC
        Keys.scanningQRCodeVCTitle: "Scanning QR code",
        Keys.sanningNotSupportTitle: "Scanning not supported",
        Keys.sanningNotSupportMessage: "Your device does not support scanning a code from an item. Please use a device with a camera.",
        // sign in
        Keys.userName: "Username",
        Keys.password: "password",
        // Activating Screen
        Keys.vtapFailedMessage: "V-OS Smart Token setup fail.",
        Keys.interupt: "Interrupt",
        Keys.splashVtapSetupFailedDialogBody: "V-OS Smart Token setup fail.",
        Keys.splashDeviceCheckFailedDialogBody: "Check the internet connection and try again.",
        Keys.accountLocked: "This device has been registered by another account before. Please contact admin!",
        Keys.registerFail: "Registration fail.",
        Keys.pushRegister: "Registering push notification...",
        Keys.registerAuthentication: "Registering authentication...",
        Keys.pkiAuthenticationFail: "PKI CSR registration fail.",
        Keys.apnError: "Register pushing notification fail. Contact admin and try again.",
        Keys.vtapErrorConnection: "Error connection",
        Keys.alertPowerOn: "Please keep device's power on while registering.",
        Keys.registerVMmessage: "Registering V-OS Secure Message...",
        Keys.createTokenPINFailed: "Create token pin failed.",
        Keys.authenticationRequestDesc: "You have an authentication request",
        // Activating Token screen
        Keys.activatingTokenInformation: "You are outside the intranet. QR code is required for token activatation.\n\n" +
            "Input your email address in the textbox and tap the Get QR Code button to obtain QR code through email",
        // Setting PIN screen
        Keys.settingPINTitle: "Setting PIN",
        Keys.confirmPINTitle: "Confirm PIN",
        Keys.changingPINTitle: "Changing PIN",
        Keys.setPin: "Set PIN",
        Keys.confirmPin: "Confirm PIN",
        Keys.oldPin: "Old PIN",
        Keys.newPin: "New PIN",
        Keys.confirmNewPin: "Confirm New PIN",
        Keys.settingPinInformation: "Set the PIN for your token. The PIN is required every time the token is used.",
        Keys.pinAlertLength : "Make sure that your pin length is 6 digits.",
        Keys.pinInvalidLength: "PIN is invalid. Make sure that your pin length is 6 digits.",
        Keys.updatePINFail: "Update PIN fail.",
        Keys.setPINFail: "Set PIN failed.",
        Keys.changePINFail: "Changing PIN failed.",
        Keys.confirmPinNotMatch: "Confirm PIN does not match with PIN",
        Keys.confirmNewPinIsSame: "New PIN is the same with old PIN",
        // Push notification
        Keys.pushNotificationTitleVNP: "VPN 2FA Authentication Request",
        Keys.pushNotificationTitleO365: "O365 2FA Authentication Request",
        Keys.pushNotificationDescVNP: "There is an authentication request for VPN login from your account. Do you want to approve the request? This request expires in 20 seconds.",
        Keys.pushNotificationDescO365: "There is an authentication request for O365 login from your account. Do you want to approve the request? This request expires in 20 seconds.",
        Keys.pinIncorrect: "PIN is incorrect.",
        Keys.oldPINIncorrect: "Old PIN is incorrect.",        
        Keys.authenticationFailed: "Authentication failed",
        Keys.biometricCheckError: "Your biometric check failed",
        Keys.biometricCheck: "Biometric check",
        Keys.bioometricLocked: "TouchID/FaceID has been locked out due to few fail attemp. Enter iPhone passcode to enable TouchID/FaceID.",
        Keys.alertPushNotiSwitchAccountTitle: "Switch Account",
        Keys.alertPushNotiSwitchAccountMessage:"You have an authentication request of %@ account. Do you want to switch to this account",
        // Home
        Keys.appName1: "VPN Login",
        Keys.appName2: "Office 365 Login",
        Keys.appName3: "Door Access",
        // account
        Keys.switchAccountMessage: "Are you sure to switch to this account?",
        Keys.service: "Service",
        Keys.platform: "Platform",
        Keys.date: "Date",
        Keys.location: "Location",
        Keys.delete: "Delete",
        Keys.viewHistory: "View History",
        Keys.askDeleteAccountTitle: "Are your sure to delete this account",
        Keys.tokenScreenTitle: "Tokens",
        Keys.loadMore: "Load more",
        Keys.pullToRefresh: "Scroll down to load more",
        // setting screen
        Keys.myTokenTitle: "My Token",
        Keys.profileSection: "PROFILE",
        Keys.settingsSection: "SETTTINGS",
        Keys.informationSection: "INFORMATION",
        Keys.email: "Email",
        Keys.enablePin: "Enable PIN",
        Keys.changePin: "Change PIN",
        Keys.enableFingerprint: "Enable Fingerprint",
        Keys.enableFaceId : "Enable Face ID",
        Keys.removeReset: "Remove (Reset)",
        Keys.transactions: "Transactions",
        Keys.about: "About",
        Keys.helps: "Helps",
        Keys.removeAccountConfirm: "Are your sure to reset",
        Keys.removeAccountFailed: "Remove account failded",
        Keys.setPINSuccessful: "Set PIN successfuly",
        Keys.changePINSuccessful: "Change PIN successfuly",
        Keys.deleteAccountSuccessful: "Delete Account successfully",
        Keys.resetAccountSuccessful: "Reset Account successfully",
        
        // image names
        ImageNameKeys.pullToRefresh: "scroll_down",
        ImageNameKeys.gridIcon: "grid_icon"
        
    ]
    
    func getValueForKey(_ key: String) -> String {
        return keysValues[key] ?? ""
    }
    func updateValueForKey(_ value: String, forKey key: String) {
        if value.count > 0 {
            keysValues.updateValue(value, forKey: key)
        }
    }
}

