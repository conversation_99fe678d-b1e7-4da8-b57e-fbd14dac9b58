//
//  UserDefaultsHandle.swift
//  V-Key
//
//  Created by <PERSON> on 5/16/19.
//  Copyright © 2019 VKey. All rights reserved.
//
// Save user to local data by [key:value]
import UIKit
class UserDefaultsHandle {
    static let sharedInstance = UserDefaultsHandle()
    // saving to local
    func saveToLocal(_ value: Any?, forKey key: String?){
        if let k = key {
            let defaults = UserDefaults.standard
            if let v = value {
                defaults.set(v, forKey: k)
            }else{
                defaults.removeObject(forKey: k)
            }
        }
    }
//    func saveToLocal(_ value: Bool?, forKey key: String?){
//        if let v = value, let k = key {
//            let defaults = UserDefaults.standard
//            defaults.set(v, forKey: k)
//        }
//    }
//    func saveToLocal(_ value: Int32?, forKey key: String?){
//        if let v = value, let k = key {
//            let defaults = UserDefaults.standard
//            defaults.set(v, forKey: k)
//        }
//    }
    func saveToLocal(_ dictionary: [String : String]){
        let defaults = UserDefaults.standard
        for (key, value) in dictionary {
            defaults.set(value, forKey: key)
        }
    }
    func saveToLocal(_ value: [[String: String]]?, forKey key: String?){
        if let v = value, let k = key {
            let defaults = UserDefaults.standard
            defaults.set(v, forKey: k)
        }
    }
    
    func getLocalForKey(_ key: String) -> Any?{
        let defaults = UserDefaults.standard
        if let value = defaults.object(forKey: key){
            return value
        }
        return nil
    }
}
