<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="Nu7-mk-8Ah">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Generator-->
        <scene sceneID="password-generator-scene">
            <objects>
                <viewController storyboardIdentifier="PasswordGeneratorViewController" id="password-generator-vc" customClass="PasswordGeneratorViewController" customModule="VKey_Diagnosis" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="password-top-guide"/>
                        <viewControllerLayoutGuide type="bottom" id="password-bottom-guide"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="password-main-view">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="🔐 Password Generator" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="password-title">
                                <rect key="frame" x="32" y="40" width="311" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="password-title-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="24"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Generated password will appear here" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="password-textfield">
                                <rect key="frame" x="32" y="90" width="311" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="password-field-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Menlo-Regular" family="Menlo" pointSize="16"/>
                                <textInputTraits key="textInputTraits"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </textField>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="options-container">
                                <rect key="frame" x="32" y="160" width="311" height="240"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Password Options" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="options-title">
                                        <rect key="frame" x="16" y="16" width="279" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="options-title-height"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="options-stack">
                                        <rect key="frame" x="16" y="52" width="279" height="172"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="length-container">
                                                <rect key="frame" x="0.0" y="0.0" width="279" height="52"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Length:" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="length-title">
                                                        <rect key="frame" x="0.0" y="16" width="60" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="length-title-height"/>
                                                            <constraint firstAttribute="width" constant="60" id="length-title-width"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="12" minValue="8" maxValue="32" translatesAutoresizingMaskIntoConstraints="NO" id="length-slider">
                                                        <rect key="frame" x="66" y="11" width="119.5" height="31"/>
                                                        <connections>
                                                            <action selector="lengthSliderChanged:" destination="password-generator-vc" eventType="valueChanged" id="slider-action"/>
                                                        </connections>
                                                    </slider>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="12 characters" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="length-label">
                                                        <rect key="frame" x="191.5" y="16" width="87.5" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="length-label-height"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="40" id="length-container-height"/>
                                                    <constraint firstItem="length-label" firstAttribute="centerY" secondItem="length-container" secondAttribute="centerY" id="length-label-center"/>
                                                    <constraint firstItem="length-label" firstAttribute="leading" secondItem="length-slider" secondAttribute="trailing" constant="8" id="length-label-leading"/>
                                                    <constraint firstAttribute="trailing" secondItem="length-label" secondAttribute="trailing" id="length-label-trailing"/>
                                                    <constraint firstItem="length-slider" firstAttribute="centerY" secondItem="length-container" secondAttribute="centerY" id="length-slider-center"/>
                                                    <constraint firstItem="length-slider" firstAttribute="leading" secondItem="length-title" secondAttribute="trailing" constant="8" id="length-slider-leading"/>
                                                    <constraint firstItem="length-title" firstAttribute="centerY" secondItem="length-container" secondAttribute="centerY" id="length-title-center"/>
                                                    <constraint firstItem="length-title" firstAttribute="leading" secondItem="length-container" secondAttribute="leading" id="length-title-leading"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="numbers-container">
                                                <rect key="frame" x="0.0" y="68" width="279" height="24"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Include Numbers (0-9)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="numbers-label">
                                                        <rect key="frame" x="0.0" y="2" width="230" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="numbers-label-height"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="numbers-switch">
                                                        <rect key="frame" x="230" y="-3.5" width="51" height="31"/>
                                                    </switch>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="24" id="numbers-container-height"/>
                                                    <constraint firstItem="numbers-label" firstAttribute="centerY" secondItem="numbers-container" secondAttribute="centerY" id="numbers-label-center"/>
                                                    <constraint firstItem="numbers-label" firstAttribute="leading" secondItem="numbers-container" secondAttribute="leading" id="numbers-label-leading"/>
                                                    <constraint firstItem="numbers-switch" firstAttribute="centerY" secondItem="numbers-container" secondAttribute="centerY" id="numbers-switch-center"/>
                                                    <constraint firstItem="numbers-switch" firstAttribute="leading" secondItem="numbers-label" secondAttribute="trailing" id="numbers-switch-leading"/>
                                                    <constraint firstAttribute="trailing" secondItem="numbers-switch" secondAttribute="trailing" id="numbers-switch-trailing"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="symbols-container">
                                                <rect key="frame" x="0.0" y="108" width="279" height="24"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Include Symbols (!@#$%)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="symbols-label">
                                                        <rect key="frame" x="0.0" y="2" width="230" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="symbols-label-height"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="symbols-switch">
                                                        <rect key="frame" x="230" y="-3.5" width="51" height="31"/>
                                                    </switch>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="24" id="symbols-container-height"/>
                                                    <constraint firstItem="symbols-label" firstAttribute="centerY" secondItem="symbols-container" secondAttribute="centerY" id="symbols-label-center"/>
                                                    <constraint firstItem="symbols-label" firstAttribute="leading" secondItem="symbols-container" secondAttribute="leading" id="symbols-label-leading"/>
                                                    <constraint firstItem="symbols-switch" firstAttribute="centerY" secondItem="symbols-container" secondAttribute="centerY" id="symbols-switch-center"/>
                                                    <constraint firstItem="symbols-switch" firstAttribute="leading" secondItem="symbols-label" secondAttribute="trailing" id="symbols-switch-leading"/>
                                                    <constraint firstAttribute="trailing" secondItem="symbols-switch" secondAttribute="trailing" id="symbols-switch-trailing"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="characters-container">
                                                <rect key="frame" x="0.0" y="148" width="279" height="24"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Include Letters (A-Z, a-z)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="characters-label">
                                                        <rect key="frame" x="0.0" y="2" width="230" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="characters-label-height"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="characters-switch">
                                                        <rect key="frame" x="230" y="-3.5" width="51" height="31"/>
                                                    </switch>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="24" id="characters-container-height"/>
                                                    <constraint firstItem="characters-label" firstAttribute="centerY" secondItem="characters-container" secondAttribute="centerY" id="characters-label-center"/>
                                                    <constraint firstItem="characters-label" firstAttribute="leading" secondItem="characters-container" secondAttribute="leading" id="characters-label-leading"/>
                                                    <constraint firstItem="characters-switch" firstAttribute="centerY" secondItem="characters-container" secondAttribute="centerY" id="characters-switch-center"/>
                                                    <constraint firstItem="characters-switch" firstAttribute="leading" secondItem="characters-label" secondAttribute="trailing" id="characters-switch-leading"/>
                                                    <constraint firstAttribute="trailing" secondItem="characters-switch" secondAttribute="trailing" id="characters-switch-trailing"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="240" id="options-container-height"/>
                                    <constraint firstAttribute="bottom" secondItem="options-stack" secondAttribute="bottom" constant="16" id="options-stack-bottom"/>
                                    <constraint firstItem="options-stack" firstAttribute="leading" secondItem="options-container" secondAttribute="leading" constant="16" id="options-stack-leading"/>
                                    <constraint firstItem="options-stack" firstAttribute="top" secondItem="options-title" secondAttribute="bottom" constant="16" id="options-stack-top"/>
                                    <constraint firstAttribute="trailing" secondItem="options-stack" secondAttribute="trailing" constant="16" id="options-stack-trailing"/>
                                    <constraint firstItem="options-title" firstAttribute="leading" secondItem="options-container" secondAttribute="leading" constant="16" id="options-title-leading"/>
                                    <constraint firstItem="options-title" firstAttribute="top" secondItem="options-container" secondAttribute="top" constant="16" id="options-title-top"/>
                                    <constraint firstAttribute="trailing" secondItem="options-title" secondAttribute="trailing" constant="16" id="options-title-trailing"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="generate-button">
                                <rect key="frame" x="32" y="420" width="311" height="50"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="generate-button-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                                <state key="normal" title="🎲 Generate Strong Password">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="generatePassword:" destination="password-generator-vc" eventType="touchUpInside" id="generate-action"/>
                                </connections>
                            </button>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" bounces="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" translatesAutoresizingMaskIntoConstraints="NO" id="description-scroll">
                                <rect key="frame" x="16" y="490" width="343" height="157"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="description-content">
                                        <rect key="frame" x="0.0" y="0.0" width="343" height="217"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Full description text with hidden word will be set programmatically" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hidden-word">
                                                <rect key="frame" x="16" y="16" width="311" height="33.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="hidden-word" secondAttribute="bottom" constant="16" id="description-content-bottom"/>
                                            <constraint firstItem="hidden-word" firstAttribute="leading" secondItem="description-content" secondAttribute="leading" constant="16" id="hidden-word-leading"/>
                                            <constraint firstItem="hidden-word" firstAttribute="top" secondItem="description-content" secondAttribute="top" constant="16" id="hidden-word-top"/>
                                            <constraint firstAttribute="trailing" secondItem="hidden-word" secondAttribute="trailing" constant="16" id="hidden-word-trailing"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="12"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="customPurpleColor"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="description-content" secondAttribute="bottom" id="description-scroll-bottom"/>
                                    <constraint firstItem="description-content" firstAttribute="leading" secondItem="description-scroll" secondAttribute="leading" id="description-scroll-leading"/>
                                    <constraint firstItem="description-content" firstAttribute="top" secondItem="description-scroll" secondAttribute="top" id="description-scroll-top"/>
                                    <constraint firstAttribute="trailing" secondItem="description-content" secondAttribute="trailing" id="description-scroll-trailing"/>
                                    <constraint firstItem="description-content" firstAttribute="width" secondItem="description-scroll" secondAttribute="width" id="description-scroll-width"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <constraints>
                            <constraint firstItem="password-bottom-guide" firstAttribute="top" secondItem="description-scroll" secondAttribute="bottom" constant="20" id="description-bottom"/>
                            <constraint firstItem="description-scroll" firstAttribute="leading" secondItem="password-main-view" secondAttribute="leading" constant="16" id="description-leading"/>
                            <constraint firstItem="description-scroll" firstAttribute="top" secondItem="generate-button" secondAttribute="bottom" constant="20" id="description-top"/>
                            <constraint firstAttribute="trailing" secondItem="description-scroll" secondAttribute="trailing" constant="16" id="description-trailing"/>
                            <constraint firstItem="generate-button" firstAttribute="leading" secondItem="password-main-view" secondAttribute="leading" constant="32" id="generate-leading"/>
                            <constraint firstItem="generate-button" firstAttribute="top" secondItem="options-container" secondAttribute="bottom" constant="20" id="generate-top"/>
                            <constraint firstAttribute="trailing" secondItem="generate-button" secondAttribute="trailing" constant="32" id="generate-trailing"/>
                            <constraint firstItem="options-container" firstAttribute="leading" secondItem="password-main-view" secondAttribute="leading" constant="32" id="options-leading"/>
                            <constraint firstItem="options-container" firstAttribute="top" secondItem="password-textfield" secondAttribute="bottom" constant="20" id="options-top"/>
                            <constraint firstAttribute="trailing" secondItem="options-container" secondAttribute="trailing" constant="32" id="options-trailing"/>
                            <constraint firstItem="password-textfield" firstAttribute="leading" secondItem="password-main-view" secondAttribute="leading" constant="32" id="password-field-leading"/>
                            <constraint firstItem="password-textfield" firstAttribute="top" secondItem="password-title" secondAttribute="bottom" constant="20" id="password-field-top"/>
                            <constraint firstAttribute="trailing" secondItem="password-textfield" secondAttribute="trailing" constant="32" id="password-field-trailing"/>
                            <constraint firstItem="password-title" firstAttribute="leading" secondItem="password-main-view" secondAttribute="leading" constant="32" id="password-title-leading"/>
                            <constraint firstItem="password-title" firstAttribute="top" secondItem="password-top-guide" secondAttribute="bottom" constant="20" id="password-title-top"/>
                            <constraint firstAttribute="trailing" secondItem="password-title" secondAttribute="trailing" constant="32" id="password-title-trailing"/>
                        </constraints>
                    </view>
                    <tabBarItem key="tabBarItem" title="Generator" image="plus" catalog="system" id="password-generator-tab-item"/>
                    <connections>
                        <outlet property="charactersSwitch" destination="characters-switch" id="characters-switch-outlet"/>
                        <outlet property="generateButton" destination="generate-button" id="generate-button-outlet"/>
                        <outlet property="hiddenWordLabel" destination="hidden-word" id="hidden-word-outlet"/>
                        <outlet property="lengthLabel" destination="length-label" id="length-label-outlet"/>
                        <outlet property="lengthSlider" destination="length-slider" id="length-slider-outlet"/>
                        <outlet property="numbersSwitch" destination="numbers-switch" id="numbers-switch-outlet"/>
                        <outlet property="passwordTextField" destination="password-textfield" id="password-field-outlet"/>
                        <outlet property="scrollView" destination="description-scroll" id="description-scroll-outlet"/>
                        <outlet property="symbolsSwitch" destination="symbols-switch" id="symbols-switch-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="password-first-responder" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-113" y="-156"/>
        </scene>
        <!--Tab Bar Controller-->
        <scene sceneID="password-tab-bar-scene">
            <objects>
                <tabBarController storyboardIdentifier="password-tab-bar" id="password-tab-bar" sceneMemberID="viewController">
                    <tabBar key="tabBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="password-tab-bar-view">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="49"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <color key="barTintColor" systemColor="customPurpleColor"/>
                    </tabBar>
                    <connections>
                        <segue destination="password-generator-vc" kind="relationship" relationship="viewControllers" id="password-generator-tab"/>
                        <segue destination="password-history-vc" kind="relationship" relationship="viewControllers" id="password-history-tab"/>
                        <segue destination="password-manager-nav" kind="relationship" relationship="viewControllers" id="password-manager-tab"/>
                    </connections>
                </tabBarController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="password-tab-bar-first-responder" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-2002" y="506"/>
        </scene>
        <!--History-->
        <scene sceneID="password-history-scene">
            <objects>
                <viewController storyboardIdentifier="PasswordHistoryViewController" id="password-history-vc" customClass="PasswordHistoryViewController" customModule="VKey_Diagnosis" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="password-history-top-guide"/>
                        <viewControllerLayoutGuide type="bottom" id="password-history-bottom-guide"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="password-history-main-view">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="password-history-header">
                                <rect key="frame" x="0.0" y="20" width="375" height="64"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Password History" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="password-history-title">
                                        <rect key="frame" x="16" y="28" width="343" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="password-history-title-height"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="customPurpleColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="64" id="password-history-header-height"/>
                                    <constraint firstItem="password-history-title" firstAttribute="centerX" secondItem="password-history-header" secondAttribute="centerX" id="password-history-title-centerx"/>
                                    <constraint firstItem="password-history-title" firstAttribute="centerY" secondItem="password-history-header" secondAttribute="centerY" constant="8" id="password-history-title-centery"/>
                                    <constraint firstItem="password-history-title" firstAttribute="leading" secondItem="password-history-header" secondAttribute="leading" constant="16" id="password-history-title-leading"/>
                                    <constraint firstAttribute="trailing" secondItem="password-history-title" secondAttribute="trailing" constant="16" id="password-history-title-trailing"/>
                                </constraints>
                            </view>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="password-history-table">
                                <rect key="frame" x="0.0" y="84" width="375" height="534"/>
                                <color key="backgroundColor" systemColor="customPurpleColor"/>
                            </tableView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No password history yet." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="password-history-empty">
                                <rect key="frame" x="32" y="356" width="311" height="19.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <constraints>
                            <constraint firstItem="password-history-empty" firstAttribute="centerX" secondItem="password-history-main-view" secondAttribute="centerX" id="password-history-empty-centerx"/>
                            <constraint firstItem="password-history-empty" firstAttribute="centerY" secondItem="password-history-main-view" secondAttribute="centerY" constant="32" id="password-history-empty-centery"/>
                            <constraint firstItem="password-history-empty" firstAttribute="leading" secondItem="password-history-main-view" secondAttribute="leading" constant="32" id="password-history-empty-leading"/>
                            <constraint firstAttribute="trailing" secondItem="password-history-empty" secondAttribute="trailing" constant="32" id="password-history-empty-trailing"/>
                            <constraint firstItem="password-history-header" firstAttribute="leading" secondItem="password-history-main-view" secondAttribute="leading" id="password-history-header-leading"/>
                            <constraint firstItem="password-history-header" firstAttribute="top" secondItem="password-history-main-view" secondAttribute="top" constant="20" id="password-history-header-top"/>
                            <constraint firstAttribute="trailing" secondItem="password-history-header" secondAttribute="trailing" id="password-history-header-trailing"/>
                            <constraint firstItem="password-history-bottom-guide" firstAttribute="top" secondItem="password-history-table" secondAttribute="bottom" id="password-history-table-bottom"/>
                            <constraint firstItem="password-history-table" firstAttribute="leading" secondItem="password-history-main-view" secondAttribute="leading" id="password-history-table-leading"/>
                            <constraint firstItem="password-history-table" firstAttribute="top" secondItem="password-history-header" secondAttribute="bottom" id="password-history-table-top"/>
                            <constraint firstAttribute="trailing" secondItem="password-history-table" secondAttribute="trailing" id="password-history-table-trailing"/>
                        </constraints>
                    </view>
                    <tabBarItem key="tabBarItem" title="History" image="clock.fill" catalog="system" id="password-history-tab-item"/>
                    <connections>
                        <outlet property="emptyStateLabel" destination="password-history-empty" id="password-history-empty-outlet"/>
                        <outlet property="tableView" destination="password-history-table" id="password-history-table-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="password-history-first-responder" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-946" y="1035"/>
        </scene>
        <!--Password Manager View Controller-->
        <scene sceneID="password-manager-scene">
            <objects>
                <viewController storyboardIdentifier="PasswordManagerViewController" id="password-manager-vc" customClass="PasswordManagerViewController" customModule="VKey_Diagnosis" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="password-manager-top-guide"/>
                        <viewControllerLayoutGuide type="bottom" id="password-manager-bottom-guide"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="password-manager-main-view">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="password-manager-table">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="618"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </tableView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="password-manager-empty">
                                <rect key="frame" x="32" y="314.5" width="311" height="38.5"/>
                                <string key="text">No saved passwords yet.
Tap + to add your first password!</string>
                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <constraints>
                            <constraint firstItem="password-manager-empty" firstAttribute="centerX" secondItem="password-manager-main-view" secondAttribute="centerX" id="password-manager-empty-centerx"/>
                            <constraint firstItem="password-manager-empty" firstAttribute="centerY" secondItem="password-manager-main-view" secondAttribute="centerY" id="password-manager-empty-centery"/>
                            <constraint firstItem="password-manager-empty" firstAttribute="leading" secondItem="password-manager-main-view" secondAttribute="leading" constant="32" id="password-manager-empty-leading"/>
                            <constraint firstAttribute="trailing" secondItem="password-manager-empty" secondAttribute="trailing" constant="32" id="password-manager-empty-trailing"/>
                            <constraint firstItem="password-manager-bottom-guide" firstAttribute="top" secondItem="password-manager-table" secondAttribute="bottom" id="password-manager-table-bottom"/>
                            <constraint firstItem="password-manager-table" firstAttribute="leading" secondItem="password-manager-main-view" secondAttribute="leading" id="password-manager-table-leading"/>
                            <constraint firstItem="password-manager-table" firstAttribute="top" secondItem="password-manager-main-view" secondAttribute="top" id="password-manager-table-top"/>
                            <constraint firstAttribute="trailing" secondItem="password-manager-table" secondAttribute="trailing" id="password-manager-table-trailing"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="emptyStateLabel" destination="password-manager-empty" id="password-manager-empty-outlet"/>
                        <outlet property="tableView" destination="password-manager-table" id="password-manager-table-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="password-manager-first-responder" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="543" y="506"/>
        </scene>
        <!--Manager-->
        <scene sceneID="password-manager-nav-scene">
            <objects>
                <navigationController id="password-manager-nav" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" title="Manager" image="key.fill" catalog="system" id="password-manager-nav-tab-item"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="password-manager-nav-bar">
                        <rect key="frame" x="0.0" y="20" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <color key="barTintColor" systemColor="customPurpleColor"/>
                    </navigationBar>
                    <connections>
                        <segue destination="password-manager-vc" kind="relationship" relationship="rootViewController" id="password-manager-root"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="password-manager-nav-first-responder" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-114" y="506"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="password-detail-scene">
            <objects>
                <viewController storyboardIdentifier="PasswordDetailViewController" id="password-detail-vc" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="password-detail-top-guide"/>
                        <viewControllerLayoutGuide type="bottom" id="password-detail-bottom-guide"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="password-detail-main-view">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="password-detail-scroll">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="password-detail-content">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="800"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Website/App" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="website-label">
                                                <rect key="frame" x="32" y="32" width="311" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="website-label-height"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Website or App name" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="detail-website-field">
                                                <rect key="frame" x="32" y="60" width="311" height="50"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="detail-website-field-height"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <textInputTraits key="textInputTraits" keyboardType="URL"/>
                                            </textField>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Username/Email" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="username-label">
                                                <rect key="frame" x="32" y="130" width="311" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="username-label-height"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Username or Email" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="detail-username-field">
                                                <rect key="frame" x="32" y="158" width="223" height="50"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="detail-username-field-height"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <textInputTraits key="textInputTraits" keyboardType="emailAddress"/>
                                            </textField>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="copy-username-button">
                                                <rect key="frame" x="263" y="158" width="80" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="copy-username-height"/>
                                                    <constraint firstAttribute="width" constant="80" id="copy-username-width"/>
                                                </constraints>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="filled" title="Copy" titleAlignment="center"/>
                                                <connections>
                                                    <action selector="copyUsernameTapped:" destination="password-detail-vc" eventType="touchUpInside" id="copy-username-action"/>
                                                </connections>
                                            </button>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Password" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="password-label">
                                                <rect key="frame" x="32" y="228" width="311" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="password-label-height"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Password" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="detail-password-field">
                                                <rect key="frame" x="32" y="256" width="311" height="50"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="detail-password-field-height"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Menlo-Regular" family="Menlo" pointSize="16"/>
                                                <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                                            </textField>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="password-buttons-stack">
                                                <rect key="frame" x="32" y="314" width="311" height="40"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="show-password-button">
                                                        <rect key="frame" x="0.0" y="0.0" width="98.5" height="40"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="show-password-height"/>
                                                        </constraints>
                                                        <state key="normal" title="Button"/>
                                                        <buttonConfiguration key="configuration" style="filled" title="👁️ Show"/>
                                                        <connections>
                                                            <action selector="showPasswordTapped:" destination="password-detail-vc" eventType="touchUpInside" id="show-password-action"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="copy-password-button">
                                                        <rect key="frame" x="106.5" y="0.0" width="98" height="40"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="copy-password-height"/>
                                                        </constraints>
                                                        <state key="normal" title="Button"/>
                                                        <buttonConfiguration key="configuration" style="filled" title="📋 Copy"/>
                                                        <connections>
                                                            <action selector="copyPasswordTapped:" destination="password-detail-vc" eventType="touchUpInside" id="copy-password-action"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="generate-password-button">
                                                        <rect key="frame" x="212.5" y="0.0" width="98.5" height="40"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="generate-password-height"/>
                                                        </constraints>
                                                        <state key="normal" title="Button"/>
                                                        <buttonConfiguration key="configuration" style="filled" title="🎲 Generate"/>
                                                        <connections>
                                                            <action selector="generatePasswordTapped:" destination="password-detail-vc" eventType="touchUpInside" id="generate-password-action"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="40" id="password-buttons-stack-height"/>
                                                </constraints>
                                            </stackView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Notes" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="notes-label">
                                                <rect key="frame" x="32" y="374" width="311" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="notes-label-height"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="notes-textview">
                                                <rect key="frame" x="32" y="402" width="311" height="120"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="120" id="notes-textview-height"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            </textView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="save-button">
                                                <rect key="frame" x="32" y="542" width="311" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="save-button-height"/>
                                                </constraints>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="filled" title="💾 Save Password"/>
                                                <connections>
                                                    <action selector="savePasswordTapped:" destination="password-detail-vc" eventType="touchUpInside" id="save-button-action"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="delete-button">
                                                <rect key="frame" x="32" y="612" width="311" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="delete-button-height"/>
                                                </constraints>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="filled" title="🗑️ Delete Password"/>
                                                <connections>
                                                    <action selector="deletePasswordTapped:" destination="password-detail-vc" eventType="touchUpInside" id="delete-button-action"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                                        <constraints>
                                            <constraint firstItem="copy-username-button" firstAttribute="leading" secondItem="detail-username-field" secondAttribute="trailing" constant="8" id="copy-username-leading"/>
                                            <constraint firstItem="copy-username-button" firstAttribute="top" secondItem="username-label" secondAttribute="bottom" constant="8" id="copy-username-top"/>
                                            <constraint firstAttribute="trailing" secondItem="copy-username-button" secondAttribute="trailing" constant="32" id="copy-username-trailing"/>
                                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="delete-button" secondAttribute="bottom" constant="32" id="delete-button-bottom"/>
                                            <constraint firstItem="delete-button" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="delete-button-leading"/>
                                            <constraint firstItem="delete-button" firstAttribute="top" secondItem="save-button" secondAttribute="bottom" constant="20" id="delete-button-top"/>
                                            <constraint firstAttribute="trailing" secondItem="delete-button" secondAttribute="trailing" constant="32" id="delete-button-trailing"/>
                                            <constraint firstItem="detail-password-field" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="detail-password-field-leading"/>
                                            <constraint firstItem="detail-password-field" firstAttribute="top" secondItem="password-label" secondAttribute="bottom" constant="8" id="detail-password-field-top"/>
                                            <constraint firstAttribute="trailing" secondItem="detail-password-field" secondAttribute="trailing" constant="32" id="detail-password-field-trailing"/>
                                            <constraint firstItem="detail-username-field" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="detail-username-field-leading"/>
                                            <constraint firstItem="detail-username-field" firstAttribute="top" secondItem="username-label" secondAttribute="bottom" constant="8" id="detail-username-field-top"/>
                                            <constraint firstItem="detail-website-field" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="detail-website-field-leading"/>
                                            <constraint firstItem="detail-website-field" firstAttribute="top" secondItem="website-label" secondAttribute="bottom" constant="8" id="detail-website-field-top"/>
                                            <constraint firstAttribute="trailing" secondItem="detail-website-field" secondAttribute="trailing" constant="32" id="detail-website-field-trailing"/>
                                            <constraint firstItem="notes-label" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="notes-label-leading"/>
                                            <constraint firstItem="notes-label" firstAttribute="top" secondItem="password-buttons-stack" secondAttribute="bottom" constant="20" id="notes-label-top"/>
                                            <constraint firstAttribute="trailing" secondItem="notes-label" secondAttribute="trailing" constant="32" id="notes-label-trailing"/>
                                            <constraint firstItem="notes-textview" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="notes-textview-leading"/>
                                            <constraint firstItem="notes-textview" firstAttribute="top" secondItem="notes-label" secondAttribute="bottom" constant="8" id="notes-textview-top"/>
                                            <constraint firstAttribute="trailing" secondItem="notes-textview" secondAttribute="trailing" constant="32" id="notes-textview-trailing"/>
                                            <constraint firstItem="password-buttons-stack" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="password-buttons-stack-leading"/>
                                            <constraint firstItem="password-buttons-stack" firstAttribute="top" secondItem="detail-password-field" secondAttribute="bottom" constant="8" id="password-buttons-stack-top"/>
                                            <constraint firstAttribute="trailing" secondItem="password-buttons-stack" secondAttribute="trailing" constant="32" id="password-buttons-stack-trailing"/>
                                            <constraint firstItem="password-label" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="password-label-leading"/>
                                            <constraint firstItem="password-label" firstAttribute="top" secondItem="detail-username-field" secondAttribute="bottom" constant="20" id="password-label-top"/>
                                            <constraint firstAttribute="trailing" secondItem="password-label" secondAttribute="trailing" constant="32" id="password-label-trailing"/>
                                            <constraint firstItem="save-button" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="save-button-leading"/>
                                            <constraint firstItem="save-button" firstAttribute="top" secondItem="notes-textview" secondAttribute="bottom" constant="20" id="save-button-top"/>
                                            <constraint firstAttribute="trailing" secondItem="save-button" secondAttribute="trailing" constant="32" id="save-button-trailing"/>
                                            <constraint firstItem="username-label" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="username-label-leading"/>
                                            <constraint firstItem="username-label" firstAttribute="top" secondItem="detail-website-field" secondAttribute="bottom" constant="20" id="username-label-top"/>
                                            <constraint firstAttribute="trailing" secondItem="username-label" secondAttribute="trailing" constant="32" id="username-label-trailing"/>
                                            <constraint firstItem="website-label" firstAttribute="leading" secondItem="password-detail-content" secondAttribute="leading" constant="32" id="website-label-leading"/>
                                            <constraint firstItem="website-label" firstAttribute="top" secondItem="password-detail-content" secondAttribute="top" constant="32" id="website-label-top"/>
                                            <constraint firstAttribute="trailing" secondItem="website-label" secondAttribute="trailing" constant="32" id="website-label-trailing"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="customPurpleColor"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="password-detail-content" secondAttribute="bottom" id="password-detail-content-bottom"/>
                                    <constraint firstItem="password-detail-content" firstAttribute="leading" secondItem="password-detail-scroll" secondAttribute="leading" id="password-detail-content-leading"/>
                                    <constraint firstItem="password-detail-content" firstAttribute="top" secondItem="password-detail-scroll" secondAttribute="top" id="password-detail-content-top"/>
                                    <constraint firstAttribute="trailing" secondItem="password-detail-content" secondAttribute="trailing" id="password-detail-content-trailing"/>
                                    <constraint firstItem="password-detail-content" firstAttribute="width" secondItem="password-detail-scroll" secondAttribute="width" id="password-detail-content-width"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <constraints>
                            <constraint firstItem="password-detail-bottom-guide" firstAttribute="top" secondItem="password-detail-scroll" secondAttribute="bottom" id="password-detail-scroll-bottom"/>
                            <constraint firstItem="password-detail-scroll" firstAttribute="leading" secondItem="password-detail-main-view" secondAttribute="leading" id="password-detail-scroll-leading"/>
                            <constraint firstItem="password-detail-scroll" firstAttribute="top" secondItem="password-detail-main-view" secondAttribute="top" id="password-detail-scroll-top"/>
                            <constraint firstAttribute="trailing" secondItem="password-detail-scroll" secondAttribute="trailing" id="password-detail-scroll-trailing"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="contentView" destination="password-detail-content" id="password-detail-content-outlet"/>
                        <outlet property="copyPasswordButton" destination="copy-password-button" id="copy-password-button-outlet"/>
                        <outlet property="copyUsernameButton" destination="copy-username-button" id="copy-username-button-outlet"/>
                        <outlet property="deleteButton" destination="delete-button" id="delete-button-outlet"/>
                        <outlet property="generatePasswordButton" destination="generate-password-button" id="generate-password-button-outlet"/>
                        <outlet property="notesTextView" destination="notes-textview" id="notes-textview-outlet"/>
                        <outlet property="passwordField" destination="detail-password-field" id="detail-password-field-outlet"/>
                        <outlet property="saveButton" destination="save-button" id="save-button-outlet"/>
                        <outlet property="scrollView" destination="password-detail-scroll" id="password-detail-scroll-outlet"/>
                        <outlet property="showPasswordButton" destination="show-password-button" id="show-password-button-outlet"/>
                        <outlet property="usernameField" destination="detail-username-field" id="detail-username-field-outlet"/>
                        <outlet property="websiteField" destination="detail-website-field" id="detail-website-field-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="password-detail-first-responder" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1201" y="506"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController storyboardIdentifier="ViewController" id="BYZ-38-t0r" customClass="ViewController" customModule="VKey_Diagnosis" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="83e-QD-bc7"/>
                        <viewControllerLayoutGuide type="bottom" id="81h-wJ-MwI"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="UeF-nQ-ZnF">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0MC-0e-tCN">
                                <rect key="frame" x="0.0" y="-65" width="375" height="104"/>
                                <subviews>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="title_bar" translatesAutoresizingMaskIntoConstraints="NO" id="P46-1q-cI9">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="104"/>
                                        <color key="backgroundColor" red="0.36319959159999998" green="0.1625019908" blue="0.4706296921" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    </imageView>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo" translatesAutoresizingMaskIntoConstraints="NO" id="p70-g0-LTK">
                                        <rect key="frame" x="22" y="31" width="83" height="73"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="83" id="AC6-3g-mfP"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="V-KEY tool" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="S0Z-Lu-WVt">
                                        <rect key="frame" x="91" y="52.5" width="234" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="234" id="cZx-w1-Zkc"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="25"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="p70-g0-LTK" firstAttribute="leading" secondItem="0MC-0e-tCN" secondAttribute="leading" constant="22" id="4Xz-OO-CwX"/>
                                    <constraint firstAttribute="trailing" secondItem="P46-1q-cI9" secondAttribute="trailing" id="9qc-dQ-qcY"/>
                                    <constraint firstItem="p70-g0-LTK" firstAttribute="top" secondItem="0MC-0e-tCN" secondAttribute="top" constant="31" id="ENO-iu-sh2"/>
                                    <constraint firstItem="S0Z-Lu-WVt" firstAttribute="centerY" secondItem="p70-g0-LTK" secondAttribute="centerY" id="GZc-Mq-krb"/>
                                    <constraint firstItem="P46-1q-cI9" firstAttribute="top" secondItem="0MC-0e-tCN" secondAttribute="top" id="LpN-4b-Q13"/>
                                    <constraint firstItem="P46-1q-cI9" firstAttribute="leading" secondItem="p70-g0-LTK" secondAttribute="trailing" constant="-105" id="QnT-MM-NJm"/>
                                    <constraint firstAttribute="bottom" secondItem="p70-g0-LTK" secondAttribute="bottom" id="ZOL-si-65y"/>
                                    <constraint firstAttribute="height" constant="104" id="gAM-9o-mOk"/>
                                    <constraint firstAttribute="bottom" secondItem="P46-1q-cI9" secondAttribute="bottom" id="nSC-pn-fbR"/>
                                    <constraint firstItem="P46-1q-cI9" firstAttribute="leading" secondItem="0MC-0e-tCN" secondAttribute="leading" id="nrV-sh-dkn"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="S0Z-Lu-WVt" secondAttribute="trailing" constant="20" symbolic="YES" id="qqJ-DB-4zj"/>
                                    <constraint firstItem="S0Z-Lu-WVt" firstAttribute="leading" secondItem="p70-g0-LTK" secondAttribute="trailing" constant="-14" id="yyN-P9-b2T"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="IDc-na-a8c">
                                <rect key="frame" x="16" y="44" width="343" height="224"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cjm-0O-7JH">
                                        <rect key="frame" x="11" y="21" width="321" height="195"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <string key="text">🛡️ Device Security Scanner  This app helps protect your device by checking for security threats and ensuring you're running in a safe environment.  Tap 'Start Security Scan' to begin checking your device for potential security issues.</string>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="cjm-0O-7JH" secondAttribute="bottom" constant="8" id="45Z-fX-RNk"/>
                                    <constraint firstItem="cjm-0O-7JH" firstAttribute="top" secondItem="IDc-na-a8c" secondAttribute="top" constant="21" id="9R8-zN-EwY"/>
                                    <constraint firstAttribute="height" constant="224" id="I70-1x-ED2"/>
                                    <constraint firstAttribute="trailing" secondItem="cjm-0O-7JH" secondAttribute="trailing" constant="11" id="ZdN-9p-Z8h"/>
                                    <constraint firstItem="cjm-0O-7JH" firstAttribute="leading" secondItem="IDc-na-a8c" secondAttribute="leading" constant="11" id="eSB-8x-o8K"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4lF-iy-ZEc">
                                <rect key="frame" x="50" y="288" width="275" height="55"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="55" id="4S6-Cx-8p1"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                                <state key="normal" title="Start Security Scan">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="press:" destination="BYZ-38-t0r" eventType="touchUpInside" id="IgF-nI-dh2"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pIX-9u-aFd">
                                <rect key="frame" x="50" y="363" width="275" height="44"/>
                                <color key="backgroundColor" systemColor="systemGreenColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="pIX-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                <state key="normal" title="Send Report via Email">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="send:" destination="BYZ-38-t0r" eventType="touchUpInside" id="UZ7-Ju-eut"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7ro-jo-e6v">
                                <rect key="frame" x="50" y="427" width="275" height="44"/>
                                <color key="backgroundColor" systemColor="systemOrangeColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="7ro-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                <state key="normal" title="Share Report Files">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="share:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Wta-Tr-F8M"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Qgd-kL-cMR">
                                <rect key="frame" x="50" y="491" width="275" height="44"/>
                                <color key="backgroundColor" systemColor="systemIndigoColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="Qgd-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                <state key="normal" title="📖 How to Use This App">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="showUserGuideVC:" destination="BYZ-38-t0r" eventType="touchUpInside" id="3US-QB-xOH"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LPf-lP-gDY">
                                <rect key="frame" x="50" y="555" width="275" height="44"/>
                                <color key="backgroundColor" systemColor="systemTealColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="LPf-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                <state key="normal" title="📋 View Detailed Results">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="showResultDetailVC:" destination="BYZ-38-t0r" eventType="touchUpInside" id="3th-7j-IIh"/>
                                </connections>
                            </button>
                            <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="TID: nil" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="STP-hM-bGH" userLabel="TID_text">
                                <rect key="frame" x="16" y="619" width="343" height="40"/>
                                <color key="backgroundColor" systemColor="systemGray6Color"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="gL5-aK-LIR"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Menlo-Regular" family="Menlo" pointSize="14"/>
                                <textInputTraits key="textInputTraits"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="8"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </textField>
                        </subviews>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <constraints>
                            <constraint firstItem="4lF-iy-ZEc" firstAttribute="top" secondItem="IDc-na-a8c" secondAttribute="bottom" constant="20" id="0BO-DS-6nE"/>
                            <constraint firstItem="4lF-iy-ZEc" firstAttribute="leading" secondItem="UeF-nQ-ZnF" secondAttribute="leading" constant="50" id="4lF-leading"/>
                            <constraint firstItem="4lF-iy-ZEc" firstAttribute="trailing" secondItem="UeF-nQ-ZnF" secondAttribute="trailing" constant="-50" id="4lF-trailing"/>
                            <constraint firstItem="STP-hM-bGH" firstAttribute="leading" secondItem="IDc-na-a8c" secondAttribute="leading" id="4pI-qP-TMc"/>
                            <constraint firstItem="IDc-na-a8c" firstAttribute="leading" secondItem="UeF-nQ-ZnF" secondAttribute="leadingMargin" id="4r1-ml-aho"/>
                            <constraint firstItem="7ro-jo-e6v" firstAttribute="leading" secondItem="4lF-iy-ZEc" secondAttribute="leading" id="7ro-leading"/>
                            <constraint firstItem="7ro-jo-e6v" firstAttribute="trailing" secondItem="4lF-iy-ZEc" secondAttribute="trailing" id="7ro-trailing"/>
                            <constraint firstItem="7ro-jo-e6v" firstAttribute="top" secondItem="pIX-9u-aFd" secondAttribute="bottom" constant="20" id="97x-Nx-KJO"/>
                            <constraint firstItem="81h-wJ-MwI" firstAttribute="top" secondItem="STP-hM-bGH" secondAttribute="bottom" constant="8" id="KyC-cV-D8I"/>
                            <constraint firstItem="LPf-lP-gDY" firstAttribute="leading" secondItem="4lF-iy-ZEc" secondAttribute="leading" id="LPf-leading"/>
                            <constraint firstItem="LPf-lP-gDY" firstAttribute="trailing" secondItem="4lF-iy-ZEc" secondAttribute="trailing" id="LPf-trailing"/>
                            <constraint firstAttribute="trailing" secondItem="0MC-0e-tCN" secondAttribute="trailing" id="Nfj-ma-HeK"/>
                            <constraint firstItem="Qgd-kL-cMR" firstAttribute="leading" secondItem="4lF-iy-ZEc" secondAttribute="leading" id="Qgd-leading"/>
                            <constraint firstItem="Qgd-kL-cMR" firstAttribute="trailing" secondItem="4lF-iy-ZEc" secondAttribute="trailing" id="Qgd-trailing"/>
                            <constraint firstItem="STP-hM-bGH" firstAttribute="top" secondItem="LPf-lP-gDY" secondAttribute="bottom" constant="20" id="TID-top"/>
                            <constraint firstItem="STP-hM-bGH" firstAttribute="trailing" secondItem="IDc-na-a8c" secondAttribute="trailing" id="WrW-MN-BGM"/>
                            <constraint firstItem="4lF-iy-ZEc" firstAttribute="centerX" secondItem="UeF-nQ-ZnF" secondAttribute="centerX" id="YDa-OW-eZd"/>
                            <constraint firstItem="LPf-lP-gDY" firstAttribute="top" secondItem="Qgd-kL-cMR" secondAttribute="bottom" constant="20" id="bPu-BC-bsT"/>
                            <constraint firstItem="0MC-0e-tCN" firstAttribute="leading" secondItem="UeF-nQ-ZnF" secondAttribute="leading" id="eDe-c4-Nb2"/>
                            <constraint firstItem="pIX-9u-aFd" firstAttribute="top" secondItem="4lF-iy-ZEc" secondAttribute="bottom" constant="20" id="nKm-3h-ygJ"/>
                            <constraint firstItem="IDc-na-a8c" firstAttribute="top" secondItem="0MC-0e-tCN" secondAttribute="bottom" constant="5" id="nRW-K8-JE2"/>
                            <constraint firstItem="pIX-9u-aFd" firstAttribute="leading" secondItem="4lF-iy-ZEc" secondAttribute="leading" id="pIX-leading"/>
                            <constraint firstItem="pIX-9u-aFd" firstAttribute="trailing" secondItem="4lF-iy-ZEc" secondAttribute="trailing" id="pIX-trailing"/>
                            <constraint firstItem="0MC-0e-tCN" firstAttribute="top" secondItem="UeF-nQ-ZnF" secondAttribute="top" constant="-22" id="tpv-Jf-cmZ"/>
                            <constraint firstItem="Qgd-kL-cMR" firstAttribute="top" secondItem="7ro-jo-e6v" secondAttribute="bottom" constant="20" id="u7c-xd-FKb"/>
                            <constraint firstItem="IDc-na-a8c" firstAttribute="trailing" secondItem="UeF-nQ-ZnF" secondAttribute="trailingMargin" id="zuo-o7-Rh1"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="biu-CS-xWW"/>
                    <connections>
                        <outlet property="TID_text" destination="STP-hM-bGH" id="SLs-gX-mSw"/>
                        <outlet property="infoView" destination="IDc-na-a8c" id="Y5D-NE-JeO"/>
                        <outlet property="lb1" destination="cjm-0O-7JH" id="2CZ-oO-OPo"/>
                        <outlet property="scanBtn" destination="4lF-iy-ZEc" id="jrX-h2-IXk"/>
                        <outlet property="sendBtn" destination="pIX-9u-aFd" id="O6T-Z0-1cF"/>
                        <outlet property="shareBtn" destination="7ro-jo-e6v" id="gBs-Ud-C7f"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="543" y="-157"/>
        </scene>
        <!--Result Detail View Controller-->
        <scene sceneID="aiv-xc-4B5">
            <objects>
                <viewController storyboardIdentifier="ResultDetailViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="rYd-k1-rbp" customClass="ResultDetailViewController" customModule="VKey_Diagnosis" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="ybm-9z-Gw6"/>
                        <viewControllerLayoutGuide type="bottom" id="zaT-jN-2IC"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="SXu-3S-yD5">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" bounces="NO" showsHorizontalScrollIndicator="NO" bouncesZoom="NO" translatesAutoresizingMaskIntoConstraints="NO" id="scroll-view-results">
                                <rect key="frame" x="0.0" y="20" width="375" height="567"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="content-view-results">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="567"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="status-container">
                                                <rect key="frame" x="16" y="16" width="343" height="120"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="system-status-card">
                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="50"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="🔍 System Check: Ready to scan" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="system-status-label">
                                                                <rect key="frame" x="12" y="12" width="319" height="26"/>
                                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemGray6Color"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="50" id="system-card-height"/>
                                                            <constraint firstAttribute="bottom" secondItem="system-status-label" secondAttribute="bottom" constant="12" id="system-label-bottom"/>
                                                            <constraint firstItem="system-status-label" firstAttribute="leading" secondItem="system-status-card" secondAttribute="leading" constant="12" id="system-label-leading"/>
                                                            <constraint firstItem="system-status-label" firstAttribute="top" secondItem="system-status-card" secondAttribute="top" constant="12" id="system-label-top"/>
                                                            <constraint firstAttribute="trailing" secondItem="system-status-label" secondAttribute="trailing" constant="12" id="system-label-trailing"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                <integer key="value" value="12"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="security-status-card">
                                                        <rect key="frame" x="0.0" y="70" width="343" height="50"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="🛡️ Security Check: Pending" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="security-status-label">
                                                                <rect key="frame" x="12" y="12" width="319" height="26"/>
                                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemGray6Color"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="50" id="security-card-height"/>
                                                            <constraint firstAttribute="bottom" secondItem="security-status-label" secondAttribute="bottom" constant="12" id="security-label-bottom"/>
                                                            <constraint firstItem="security-status-label" firstAttribute="leading" secondItem="security-status-card" secondAttribute="leading" constant="12" id="security-label-leading"/>
                                                            <constraint firstItem="security-status-label" firstAttribute="top" secondItem="security-status-card" secondAttribute="top" constant="12" id="security-label-top"/>
                                                            <constraint firstAttribute="trailing" secondItem="security-status-label" secondAttribute="trailing" constant="12" id="security-label-trailing"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                <integer key="value" value="12"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="security-status-card" firstAttribute="leading" secondItem="status-container" secondAttribute="leading" id="security-card-leading"/>
                                                    <constraint firstItem="security-status-card" firstAttribute="top" secondItem="system-status-card" secondAttribute="bottom" constant="20" id="security-card-top"/>
                                                    <constraint firstAttribute="trailing" secondItem="security-status-card" secondAttribute="trailing" id="security-card-trailing"/>
                                                    <constraint firstAttribute="height" constant="120" id="status-container-height"/>
                                                    <constraint firstItem="system-status-card" firstAttribute="leading" secondItem="status-container" secondAttribute="leading" id="system-card-leading"/>
                                                    <constraint firstItem="system-status-card" firstAttribute="top" secondItem="status-container" secondAttribute="top" id="system-card-top"/>
                                                    <constraint firstAttribute="trailing" secondItem="system-status-card" secondAttribute="trailing" id="system-card-trailing"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LN9-Gt-2Jx">
                                                <rect key="frame" x="16" y="156" width="343" height="267.5"/>
                                                <string key="text">SECURITY ALERT
We detected 5 security threats on your device:
! Threat 1:
• Name: 99
• Type: 1000
• Details: Jailbroken Detected by VOS
A Threat 2:
• Name: 1
• Type: 1000
• Details: /bin/bash
! Threat 3:
• Name: 24
• Type: 2000
• Details:</string>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="LN9-Gt-2Jx" secondAttribute="bottom" constant="16" id="content-bottom"/>
                                            <constraint firstItem="LN9-Gt-2Jx" firstAttribute="leading" secondItem="content-view-results" secondAttribute="leading" constant="16" id="content-leading"/>
                                            <constraint firstAttribute="trailing" secondItem="LN9-Gt-2Jx" secondAttribute="trailing" constant="16" id="content-trailing"/>
                                            <constraint firstItem="LN9-Gt-2Jx" firstAttribute="top" secondItem="status-container" secondAttribute="bottom" constant="20" id="result-top"/>
                                            <constraint firstItem="status-container" firstAttribute="leading" secondItem="content-view-results" secondAttribute="leading" constant="16" id="status-leading"/>
                                            <constraint firstItem="status-container" firstAttribute="top" secondItem="content-view-results" secondAttribute="top" constant="16" id="status-top"/>
                                            <constraint firstAttribute="trailing" secondItem="status-container" secondAttribute="trailing" constant="16" id="status-trailing"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="customPurpleColor"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="content-view-results" secondAttribute="bottom" id="scroll-content-bottom"/>
                                    <constraint firstItem="content-view-results" firstAttribute="leading" secondItem="scroll-view-results" secondAttribute="leading" id="scroll-content-leading"/>
                                    <constraint firstItem="content-view-results" firstAttribute="top" secondItem="scroll-view-results" secondAttribute="top" id="scroll-content-top"/>
                                    <constraint firstAttribute="trailing" secondItem="content-view-results" secondAttribute="trailing" id="scroll-content-trailing"/>
                                    <constraint firstItem="content-view-results" firstAttribute="width" secondItem="scroll-view-results" secondAttribute="width" id="scroll-content-width"/>
                                </constraints>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xq3-ZT-2Yl">
                                <rect key="frame" x="32" y="607" width="311" height="44"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="back-button-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                <state key="normal" title="← Back to Main Screen">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="backAction:" destination="rYd-k1-rbp" eventType="touchUpInside" id="OiR-Eo-F73"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <constraints>
                            <constraint firstItem="zaT-jN-2IC" firstAttribute="top" secondItem="xq3-ZT-2Yl" secondAttribute="bottom" constant="16" id="button-bottom"/>
                            <constraint firstItem="xq3-ZT-2Yl" firstAttribute="leading" secondItem="SXu-3S-yD5" secondAttribute="leading" constant="32" id="button-leading"/>
                            <constraint firstItem="xq3-ZT-2Yl" firstAttribute="top" secondItem="scroll-view-results" secondAttribute="bottom" constant="20" id="button-top"/>
                            <constraint firstAttribute="trailing" secondItem="xq3-ZT-2Yl" secondAttribute="trailing" constant="32" id="button-trailing"/>

                            <constraint firstItem="scroll-view-results" firstAttribute="leading" secondItem="SXu-3S-yD5" secondAttribute="leading" id="scroll-leading"/>
                            <constraint firstItem="scroll-view-results" firstAttribute="top" secondItem="ybm-9z-Gw6" secondAttribute="bottom" id="scroll-top"/>
                            <constraint firstAttribute="trailing" secondItem="scroll-view-results" secondAttribute="trailing" id="scroll-trailing"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="Tlr-kE-1iq"/>
                    <connections>
                        <outlet property="resultString" destination="LN9-Gt-2Jx" id="5NA-mO-Bkh"/>
                        <outlet property="scrollView" destination="scroll-view-results" id="scroll-outlet"/>
                        <outlet property="securityStatusCard" destination="security-status-card" id="security-card-outlet"/>
                        <outlet property="securityStatusLabel" destination="security-status-label" id="security-status-outlet"/>
                        <outlet property="systemStatusCard" destination="system-status-card" id="system-card-outlet"/>
                        <outlet property="systemStatusLabel" destination="system-status-label" id="system-status-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="wqZ-U2-Vw7" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1860" y="-156"/>
        </scene>
        <!--User Guide View Controller-->
        <scene sceneID="bcx-eg-P3l">
            <objects>
                <viewController storyboardIdentifier="UserGuideViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="28i-LQ-lHD" customClass="UserGuideViewController" customModule="VKey_Diagnosis" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="g1X-5J-Gbh"/>
                        <viewControllerLayoutGuide type="bottom" id="cNC-N8-rp5"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Mms-99-J6H">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" bounces="NO" showsHorizontalScrollIndicator="NO" bouncesZoom="NO" translatesAutoresizingMaskIntoConstraints="NO" id="user-guide-scroll">
                                <rect key="frame" x="0.0" y="20" width="375" height="567"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="user-guide-content">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="567"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xfw-mj-QRU">
                                                <rect key="frame" x="16" y="16" width="343" height="363"/>
                                                <string key="text">Device Security Scanner Guide
Welcome to the Device Security Scanner! This app helps protect your device by checking for security threats and vulnerabilities.
How to Perform a Security Scan:
1. Start the Scan
• Tap "Start Security Scan" on the main screen
• The app will begin checking your device for threats
• Wait for the scan to complete (this may take a few moments)
2. Review Results
• Once complete, check the status indicators:
V
Green = Secure/Safe
Red = Threats detected
! Yellow = Issues found
• Tap "View Detailed Results" for more information</string>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="Xfw-mj-QRU" secondAttribute="bottom" constant="16" id="guide-content-bottom"/>
                                            <constraint firstItem="Xfw-mj-QRU" firstAttribute="leading" secondItem="user-guide-content" secondAttribute="leading" constant="16" id="guide-content-leading"/>
                                            <constraint firstItem="Xfw-mj-QRU" firstAttribute="top" secondItem="user-guide-content" secondAttribute="top" constant="16" id="guide-content-top"/>
                                            <constraint firstAttribute="trailing" secondItem="Xfw-mj-QRU" secondAttribute="trailing" constant="16" id="guide-content-trailing"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="customPurpleColor"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="user-guide-content" secondAttribute="bottom" id="guide-scroll-bottom"/>
                                    <constraint firstItem="user-guide-content" firstAttribute="leading" secondItem="user-guide-scroll" secondAttribute="leading" id="guide-scroll-leading"/>
                                    <constraint firstItem="user-guide-content" firstAttribute="top" secondItem="user-guide-scroll" secondAttribute="top" id="guide-scroll-top"/>
                                    <constraint firstAttribute="trailing" secondItem="user-guide-content" secondAttribute="trailing" id="guide-scroll-trailing"/>
                                    <constraint firstItem="user-guide-content" firstAttribute="width" secondItem="user-guide-scroll" secondAttribute="width" id="guide-scroll-width"/>
                                </constraints>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="L56-7R-ioP">
                                <rect key="frame" x="32" y="607" width="311" height="44"/>
                                <color key="backgroundColor" systemColor="systemBlueColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="guide-back-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                <state key="normal" title="← Back to Main Screen">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="back:" destination="28i-LQ-lHD" eventType="touchUpInside" id="nbZ-GQ-k4I"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="customPurpleColor"/>
                        <constraints>
                            <constraint firstItem="cNC-N8-rp5" firstAttribute="top" secondItem="L56-7R-ioP" secondAttribute="bottom" constant="16" id="guide-button-bottom"/>
                            <constraint firstItem="L56-7R-ioP" firstAttribute="leading" secondItem="Mms-99-J6H" secondAttribute="leading" constant="32" id="guide-button-leading"/>
                            <constraint firstItem="L56-7R-ioP" firstAttribute="top" secondItem="user-guide-scroll" secondAttribute="bottom" constant="20" id="guide-button-top"/>
                            <constraint firstAttribute="trailing" secondItem="L56-7R-ioP" secondAttribute="trailing" constant="32" id="guide-button-trailing"/>
                            <constraint firstItem="user-guide-scroll" firstAttribute="leading" secondItem="Mms-99-J6H" secondAttribute="leading" id="guide-scroll-leading-constraint"/>
                            <constraint firstItem="user-guide-scroll" firstAttribute="top" secondItem="g1X-5J-Gbh" secondAttribute="bottom" id="guide-scroll-top-constraint"/>
                            <constraint firstAttribute="trailing" secondItem="user-guide-scroll" secondAttribute="trailing" id="guide-scroll-trailing-constraint"/>

                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="1Gb-vM-heH"/>
                    <connections>
                        <outlet property="contentLabel" destination="Xfw-mj-QRU" id="guide-content-outlet"/>
                        <outlet property="scrollView" destination="user-guide-scroll" id="guide-scroll-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ya1-fd-1MB" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1202" y="-156"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="yzM-TM-P3W">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" navigationBarHidden="YES" id="Nu7-mk-8Ah" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="Hvq-Ia-3tg">
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="BYZ-38-t0r" kind="relationship" relationship="rootViewController" id="mMQ-7E-kvm"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="cAN-r1-ypc" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-2674" y="506"/>
        </scene>
    </scenes>
    <resources>
        <image name="clock.fill" catalog="system" width="128" height="123"/>
        <image name="key.fill" catalog="system" width="82" height="128"/>
        <image name="logo" width="2084.5" height="2084.5"/>
        <image name="plus" catalog="system" width="128" height="113"/>
        <image name="title_bar" width="810" height="202"/>
        <systemColor name="customPurpleColor">
            <color red="0.4549019608" green="0.1960784314" blue="0.58431372550000005" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBlueColor">
            <color red="0.0" green="0.47843137250000001" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemGray6Color">
            <color red="0.94901960780000005" green="0.94901960780000005" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemGreenColor">
            <color red="0.20392156859999999" green="0.78039215689999997" blue="0.34901960780000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemIndigoColor">
            <color red="0.34509803919999998" green="0.33725490200000002" blue="0.83921568629999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemOrangeColor">
            <color red="1" green="0.58431372550000005" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemTealColor">
            <color red="0.18823529410000001" green="0.69019607839999997" blue="0.78039215689999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
