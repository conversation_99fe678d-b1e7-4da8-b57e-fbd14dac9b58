//
//  UIViewController+ScrollForKeyboard.swift
//  V-Key
//
//  Created by <PERSON> on 6/3/19.
//  Copyright © 2019 VKey. All rights reserved.
//


/* add scrollview to handle keyboard show/hide, when keyboard shows, the scrollview scroll up tho show buttons, when keyboard hides, sroll back */
import UIKit
extension UIViewController {
    // replace the main view of viewcontroller, by other view which has a scroll view, this scroll view brings the main view
    func addScrollViewHasSubView(_ subview: UIView?){
        let mainView = UIView()
        var gap: CGFloat = 0
        if checkNavBarExisted() {
            gap = getBarHeight()
            let selfFrame = UIScreen.main.bounds
            self.view.frame = CGRect(x: selfFrame.origin.x, y: selfFrame.origin.y, width: selfFrame.size.width, height: selfFrame.size.height - gap)
        }
        mainView.backgroundColor = self.view.backgroundColor
        mainView.frame = self.view.frame
        let scrollView = UIScrollView()
        let oldMainView = self.view as UIView
        let frame = oldMainView.frame
        scrollView.frame = CGRect(x: frame.origin.x, y: gap, width: frame.size.width, height: frame.size.height)
        scrollView.backgroundColor = UIColor.blue
        mainView.addSubview(scrollView)
        scrollView.showsVerticalScrollIndicator = false
        scrollView.contentSize = scrollView.frame.size // CGSize(width: oldMainView.frame.size.width, height: oldMainView.frame.size.height - 0)
        self.view = mainView
        
        let button = UIButton()
        oldMainView.addSubview(button)
        oldMainView.sendSubviewToBack(button)
        button.backgroundColor = UIColor.clear
        button.frame = oldMainView.bounds
        
        scrollView.backgroundColor = UIColor.clear
        
        scrollView.tag = 10001
        
        button.addTarget(self, action: #selector(self.handleTapToHideKeyboard(_:)), for: .touchUpInside)
        
        scrollView.addSubview(oldMainView)
        if let subview = subview {
            subview.tag = 10000
        }
    }
    // check that the navigation bar exist or not
    func checkNavBarExisted()->Bool{
        if let viewControllers = self.navigationController?.viewControllers
        {
            for vc in viewControllers {
                if vc == self {
                    return true
                }
            }
        }
        return false
    }
    // get height of navigation bar & status bar
    func getBarHeight()->CGFloat{
        var height = UIApplication.shared.statusBarFrame.height
        if let nav = self.navigationController{
            height += nav.navigationBar.frame.height
        }
        return height
    }
    // add keyboard observer to self
    func addKeyboardObjservers(){
        // handle keyboard
        let notificationCenter = NotificationCenter.default
        notificationCenter.addObserver(self, selector: #selector(adjustForKeyboard), name: UIResponder.keyboardWillHideNotification, object: nil)
        notificationCenter.addObserver(self, selector: #selector(adjustForKeyboard), name: UIResponder.keyboardWillChangeFrameNotification, object: nil)
    }
    func removeKeyboardObservers(){
        NotificationCenter.default.removeObserver(self)
    }
    // hide the keyboard when user clicks 'background' button
    @objc func handleTapToHideKeyboard(_ gesture: UIGestureRecognizer){
        self.view.endEditing(true)
    }
    // this func is invoked when keyboard show/hide
    @objc func adjustForKeyboard(notification: Notification) {
        guard let keyboardValue = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue else { return }
        guard let scrollView = self.view.viewWithTag(10001) as? UIScrollView else { return}
        
        let keyboardScreenEndFrame = keyboardValue.cgRectValue
        let keyboardViewEndFrame = self.view.convert(keyboardScreenEndFrame, from: self.view.window)
        
        if notification.name == UIResponder.keyboardWillHideNotification {
            // scroll back when keyboard hides
            let contentInsets = UIEdgeInsets.zero
            scrollView.contentInset = contentInsets
            scrollView.scrollIndicatorInsets = contentInsets
        } else { // keyboard shows:
            if let userInfo = notification.userInfo, let durationValue = userInfo[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double, let curveValue = userInfo[UIResponder.keyboardAnimationCurveUserInfoKey] as? UInt {
                // Transform the keyboard's frame into our view's coordinate system
                // Find out how much the keyboard overlaps the scroll view
                let keyboardOverlap = scrollView.frame.maxY - keyboardViewEndFrame.origin.y
                
                // Set the scroll view's content inset to avoid the keyboard
                // Don't forget the scroll indicator too!
                scrollView.contentInset.bottom = keyboardOverlap
                scrollView.scrollIndicatorInsets.bottom = keyboardOverlap
                
                // show sub view if it existed
                if let subview = self.view.viewWithTag(10000) {
                    let frameOfSubview = subview.frame
                    let frameOfVisible = CGRect(x: frameOfSubview.origin.x, y: frameOfSubview.origin.y + frameOfSubview.size.height - 10, width: 10, height: 10)
                    scrollView.scrollRectToVisible(frameOfVisible, animated: true)
                }
                // animation
                let duration = durationValue
                let options = UIView.AnimationOptions(rawValue: UInt(curveValue << 16))
                UIView.animate(withDuration: duration, delay: 0, options: options, animations: {
                    self.view.layoutIfNeeded()
                }, completion: nil)
            }            
        }
    }
}

