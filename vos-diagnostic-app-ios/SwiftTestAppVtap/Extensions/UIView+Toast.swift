//
//  UIView+Toast.swift
//  V-Key
//
//  Created by <PERSON> on 5/24/19.
//  Copyright © 2019 VKey. All rights reserved.
//

/* Show toast message in view
 */
import UIKit
extension UIView {
    func showToastMessage(_ message: String?){
        guard let _ = message else {
            return
        }
        let toastTextField = UITextField()
        self.addSubview(toastTextField)
        
        toastTextField.text = message
        toastTextField.textColor = UIColor.white
        toastTextField.textAlignment = .center
        toastTextField.layer.shadowColor = UIColor.blue.cgColor
        toastTextField.layer.cornerRadius = 8
        toastTextField.backgroundColor = UIColor(red: 80.0/255, green: 80.0/255, blue: 80.0/255, alpha: 1)
        toastTextField.layer.shadowOffset = CGSize(width: 2, height: 2)
        
        var minTextSize = toastTextField.intrinsicContentSize
        minTextSize = CGSize(width: minTextSize.width + 22, height: minTextSize.height + 18)
        toastTextField.translatesAutoresizingMaskIntoConstraints = false
        toastTextField.bottomAnchor.constraint(equalTo: self.bottomAnchor, constant: -80).isActive = true
        toastTextField.centerXAnchor.constraint(equalTo: self.centerXAnchor).isActive = true
        toastTextField.heightAnchor.constraint(equalToConstant: minTextSize.height).isActive = true
        toastTextField.widthAnchor.constraint(equalToConstant: minTextSize.width).isActive = true
        
        // show toast animately
        toastTextField.alpha = 0
        UIView.animate(withDuration: 0.5, delay: 0.0, options: .curveLinear, animations: {
            toastTextField.alpha = 1
        }, completion: nil)
        
        // remove toastTextField in 2 second
        UIView.animate(withDuration: 0.5, delay: 1.5, options: .curveLinear, animations: {
            toastTextField.alpha = 0
        }) { _ in
            toastTextField.removeFromSuperview()
        }
    }
    
}
