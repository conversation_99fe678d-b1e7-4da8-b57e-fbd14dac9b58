////
////  Observable+Extensions.swift
////  V-Key
////
////  Created by <PERSON> on 5/16/19.
////  Copyright © 2019 VKey. All rights reserved.
////
//
//import Foundation
//import RxSwift
//
//public protocol OptionalType {
//    associatedtype Wrapped
//    
//    var optional: Wrapped? { get }
//}
//
//extension Optional: OptionalType {
//    public var optional: Wrapped? { return self }
//}
//
//// Ignore when data is nil.
//extension Observable where Element: OptionalType {
//    func ignoreNil() -> Observable<Element.Wrapped> {
//        return flatMap { value in
//            value.optional.map { Observable<Element.Wrapped>.just($0) } ?? Observable<Element.Wrapped>.empty()
//        }
//    }
//}
