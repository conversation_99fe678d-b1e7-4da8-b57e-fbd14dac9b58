import UIKit
extension Data {
    // convert data to hexa decimal string
    func hexadecimalString() -> String? {
        // Returns hexadecimal string of NSData. Empty string if data is empty.
        let dataBuffer = [UInt8](self)
        
        let dataLength = count
        var hexString: String = ""
        
        for i in 0..<dataLength {
            hexString += String(format: "%02lx", UInt(dataBuffer[i]))
        }
        
        return hexString
    }

    static func dataFromBase64String(aString: String?) -> Data?{
        return (aString?.data(using: String.Encoding.ascii))
    }
}


