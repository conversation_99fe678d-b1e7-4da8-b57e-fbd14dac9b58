//
//  ResultDetailViewController.swift
//  VKey-Diagnosis
//
//  Created by ADMIN on 7/20/21.
//  Copyright © 2021 com.vkey. All rights reserved.
//

import UIKit

class ResultDetailViewController: UIViewController {
    @IBOutlet weak var resultString: UILabel!
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var systemStatusLabel: UILabel!
    @IBOutlet weak var securityStatusLabel: UILabel!
    @IBOutlet weak var systemStatusCard: UIView!
    @IBOutlet weak var securityStatusCard: UIView!

    var resultString_prepare: String?
    var systemStatus: Int = -1
    var securityStatus: Int = -1

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupContent()
    }

    private func setupUI() {
        // Configure navigation
        navigationItem.title = "Scan Results"
        navigationController?.navigationBar.prefersLargeTitles = false

        // Setup modern styling for the result label
        resultString.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        resultString.textColor = UIColor.white
        resultString.numberOfLines = 0
        resultString.lineBreakMode = .byWordWrapping

        // Add padding to the scroll view content
        if let scrollView = scrollView {
            scrollView.contentInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
            scrollView.backgroundColor = UIColor(red: 0.4549019608, green: 0.1960784314, blue: 0.58431372550000005, alpha: 1.0)
        }

        // Setup status displays
        setupStatusDisplays()

        // Set purple background color
        view.backgroundColor = UIColor(red: 0.4549019608, green: 0.1960784314, blue: 0.58431372550000005, alpha: 1.0)
    }

    private func setupStatusDisplays() {
        // Configure status labels
        systemStatusLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        systemStatusLabel?.textColor = UIColor.darkText

        securityStatusLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        securityStatusLabel?.textColor = UIColor.darkText

        // Configure status cards
        systemStatusCard?.layer.cornerRadius = 12
        systemStatusCard?.backgroundColor = UIColor(red: 0.95, green: 0.95, blue: 0.98, alpha: 0.95)

        securityStatusCard?.layer.cornerRadius = 12
        securityStatusCard?.backgroundColor = UIColor(red: 0.95, green: 0.95, blue: 0.98, alpha: 0.95)

        // Update status displays
        updateStatusDisplays()
    }

    private func setupContent() {
        if let content = resultString_prepare, !content.isEmpty {
            // Format the content for better readability
            let formattedContent = formatResultContent(content)
            resultString.text = formattedContent
        } else {
            // Show a friendly message when no scan has been performed
            resultString.text = """
            📱 No Scan Results Yet

            To view your device security results:

            1. Go back to the main screen
            2. Tap 'Scan'
            3. Wait for the scan to complete
            4. Return "Result Detail" to view detailed results

            The scan will check your device and provide a report.
            """
        }
    }

    private func formatResultContent(_ content: String) -> String {
        // Improve formatting of the result content
        var formatted = content

        // Add better spacing and formatting
        formatted = formatted.replacingOccurrences(of: "\\n\\n", with: "\n\n")
        formatted = formatted.replacingOccurrences(of: "\\n", with: "\n")

        // Add section headers formatting
        if formatted.contains("Security Alert") {
            formatted = formatted.replacingOccurrences(of: "🚨 Security Alert", with: "🚨 SECURITY ALERT")
        }

        if formatted.contains("Excellent!") {
            formatted = formatted.replacingOccurrences(of: "✅ Excellent!", with: "✅ DEVICE CLEAN")
        }

        return formatted
    }

    private func updateStatusDisplays() {
        // Update system status
        if systemStatus == -1 {
            systemStatusLabel?.text = "🔍 System Check: Ready to scan"
            systemStatusCard?.backgroundColor = UIColor(red: 0.95, green: 0.95, blue: 0.98, alpha: 0.95)
        } else if systemStatus == VOS_OK.rawValue {
            systemStatusLabel?.text = "✅ VOS: VOS_OK"
            systemStatusCard?.backgroundColor = UIColor.systemGreen.withAlphaComponent(0.5)
        } else {
            systemStatusLabel?.text = "⚠️ VOS: \(systemStatus)"
            systemStatusCard?.backgroundColor = UIColor.systemRed.withAlphaComponent(0.3)
        }

        // Update security status
        if securityStatus == -1 {
            securityStatusLabel?.text = "🛡️ Security Check: Pending"
            securityStatusCard?.backgroundColor = UIColor(red: 0.95, green: 0.95, blue: 0.98, alpha: 0.95)
        } else if securityStatus == VGUARD_SAFE.rawValue {
            securityStatusLabel?.text = "✅ VGuard: VGUARD_SAFE"
            securityStatusCard?.backgroundColor = UIColor.systemGreen.withAlphaComponent(0.5)
        } else if securityStatus == VGUARD_UNSAFE.rawValue {
            securityStatusLabel?.text = "🚨 VGuard: VGUARD_UNSAFE"
            securityStatusCard?.backgroundColor = UIColor.systemRed.withAlphaComponent(0.3)
        } else {
            securityStatusLabel?.text = "❓ Security Check: Unknown status"
            securityStatusCard?.backgroundColor = UIColor.systemYellow.withAlphaComponent(0.3)
        }
    }

    func updateStatus(systemStatus: Int, securityStatus: Int) {
        self.systemStatus = systemStatus
        self.securityStatus = securityStatus

        // Update displays if view is loaded
        if isViewLoaded {
            updateStatusDisplays()
        }
    }

    @IBAction func backAction(_ sender: Any) {
        self.navigationController?.popViewController(animated: true)
    }
}

